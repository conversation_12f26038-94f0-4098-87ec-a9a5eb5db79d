package com.jinghang.cash.enums;

import lombok.Getter;

/**
 * @Classname ResultCode
 * @Description 响应code
 * @Date 2023/11/15 19:41
 * @Created by gale
 */
@Getter
public enum ResultCode {

    SUCCESS("000000", "success"),
    PARAM_ILLEGAL("100000", "参数不合法"),
    BIZ_ERROR("100001", "业务异常"),
    NO_SUBMIT_REPEAT("555555", "请勿重复提交"),
    SAME_DATA("100002", "数据重复"),
    SYS_ERROR("999999", "系统异常"),
    QUERY_RECORDS_FAIL("100017", "查询不到记录"),;


    private final String code;
    private final String msg;

    ResultCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static boolean isSuccess(String code) {
        return SUCCESS.getCode().equals(code);
    }
}
