package com.jinghang.cash.utils;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;

import java.io.File;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-08-06
 */
public class ExcelUtils {

    /**
     * 构建输出流
     *
     * @param data               数据
     * @param headerAlias        headerAlias 表头别名映射关系
     */
    public static void buildInputStream(List<LinkedHashMap<String, Object>> data, LinkedHashMap<String, String> headerAlias, File file) {
        try {
            ExcelWriter writer = ExcelUtil.getWriter(file);
            writer.getStyleSet().setAlign(HorizontalAlignment.LEFT, VerticalAlignment.CENTER);
            writer.setHeaderAlias(headerAlias);
            writer.write(data, Boolean.TRUE);
            writer.autoSizeColumnAll();
            writer.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 构建输出流
     *
     * @param mapData    多个sheet数据
     * @param headerAlias headerAlias 表头别名映射关系
     */
    public static void buildInputStream(Map<String, List<LinkedHashMap<String, Object>>> mapData, LinkedHashMap<String, String> headerAlias, File file) {
        try {
            ExcelWriter writer = ExcelUtil.getWriter(file);
            writer.setHeaderAlias(headerAlias);
            writer.getStyleSet().setAlign(HorizontalAlignment.LEFT, VerticalAlignment.CENTER);
            int sheetNo = 0;
            for (Map.Entry<String, List<LinkedHashMap<String, Object>>> entry : mapData.entrySet()) {
                String key = entry.getKey();
                if (sheetNo == 0) {
                    writer.getWorkbook().setSheetName(sheetNo, key);
                } else {
                    writer.setSheet(key);
                }
                writer.write(mapData.get(key));
                writer.autoSizeColumnAll();
                sheetNo++;
            }
            writer.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
