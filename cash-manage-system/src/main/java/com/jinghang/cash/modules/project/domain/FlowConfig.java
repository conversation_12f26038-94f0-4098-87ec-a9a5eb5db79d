/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.project.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.sql.Timestamp;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* @description /
* <AUTHOR>
* @date 2025-08-21
**/
@Data
@TableName("flow_config")
public class FlowConfig implements Serializable {

    @TableId(value = "id")
    @ApiModelProperty(value = "主键")
    private String id;

    @NotBlank
    @ApiModelProperty(value = "流量渠道")
    private String flowChannel;

    @NotNull
    @ApiModelProperty(value = "流量授信日限额")
    private BigDecimal creditDayAmt;

    @NotNull
    @ApiModelProperty(value = "流量放款日限额")
    private BigDecimal loanDayAmt;

    @ApiModelProperty(value = "第一绑卡渠道")
    private String firstProtocolChannel;

    @ApiModelProperty(value = "第二绑卡渠道")
    private String secondProtocolChannel;

    @NotBlank
    @ApiModelProperty(value = "启用状态 1 DISABLE 2 ENABLE")
    private String enabled;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "乐观锁")
    private String revision;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private Timestamp createdTime;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    private Timestamp updatedTime;

    @ApiModelProperty(value = "资产方简称")
    private String flowNameShort;

    @ApiModelProperty(value = "资产方主体全称")
    private String flowName;

    @ApiModelProperty(value = "资方简介")
    private String flowDesc;

    @ApiModelProperty(value = "联系人")
    private String contactPerson;

    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    @ApiModelProperty(value = "邮箱地址")
    private String emailAddress;

    public void copy(FlowConfig source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
