/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package project.rest;

import me.zhengjie.annotation.Log;
import project.domain.FlowConfig;
import project.service.FlowConfigService;
import project.domain.dto.FlowConfigQueryCriteria;
import lombok.RequiredArgsConstructor;
import java.util.List;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import me.zhengjie.utils.PageResult;

/**
* <AUTHOR>
* @date 2025-08-21
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "资产控制")
@RequestMapping("/api/flowConfig")
public class FlowConfigController {

    private final FlowConfigService flowConfigService;

    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('flowConfig:list')")
    public void exportFlowConfig(HttpServletResponse response, FlowConfigQueryCriteria criteria) throws IOException {
        flowConfigService.download(flowConfigService.queryAll(criteria), response);
    }

    @GetMapping
    @ApiOperation("查询资产控制")
    @PreAuthorize("@el.check('flowConfig:list')")
    public ResponseEntity<PageResult<FlowConfig>> queryFlowConfig(FlowConfigQueryCriteria criteria){
        Page<Object> page = new Page<>(criteria.getPage(), criteria.getSize());
        return new ResponseEntity<>(flowConfigService.queryAll(criteria,page),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增资产控制")
    @ApiOperation("新增资产控制")
    @PreAuthorize("@el.check('flowConfig:add')")
    public ResponseEntity<Object> createFlowConfig(@Validated @RequestBody FlowConfig resources){
        flowConfigService.create(resources);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改资产控制")
    @ApiOperation("修改资产控制")
    @PreAuthorize("@el.check('flowConfig:edit')")
    public ResponseEntity<Object> updateFlowConfig(@Validated @RequestBody FlowConfig resources){
        flowConfigService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除资产控制")
    @ApiOperation("删除资产控制")
    @PreAuthorize("@el.check('flowConfig:del')")
    public ResponseEntity<Object> deleteFlowConfig(@ApiParam(value = "传ID数组[]") @RequestBody List<String> ids) {
        flowConfigService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}