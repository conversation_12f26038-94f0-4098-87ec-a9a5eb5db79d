/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package project.service.impl;

import project.domain.FlowConfig;
import me.zhengjie.utils.FileUtil;
import lombok.RequiredArgsConstructor;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import project.service.FlowConfigService;
import project.domain.dto.FlowConfigQueryCriteria;
import project.mapper.FlowConfigMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import me.zhengjie.utils.PageUtil;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import me.zhengjie.utils.PageResult;

/**
* @description 服务实现
* <AUTHOR>
* @date 2025-08-21
**/
@Service
@RequiredArgsConstructor
public class FlowConfigServiceImpl extends ServiceImpl<FlowConfigMapper, FlowConfig> implements FlowConfigService {

    private final FlowConfigMapper flowConfigMapper;

    @Override
    public PageResult<FlowConfig> queryAll(FlowConfigQueryCriteria criteria, Page<Object> page){
        return PageUtil.toPage(flowConfigMapper.findAll(criteria, page));
    }

    @Override
    public List<FlowConfig> queryAll(FlowConfigQueryCriteria criteria){
        return flowConfigMapper.findAll(criteria);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(FlowConfig resources) {
        flowConfigMapper.insert(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(FlowConfig resources) {
        FlowConfig flowConfig = getById(resources.getId());
        flowConfig.copy(resources);
        flowConfigMapper.updateById(flowConfig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAll(List<String> ids) {
        flowConfigMapper.deleteBatchIds(ids);
    }

    @Override
    public void download(List<FlowConfig> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (FlowConfig flowConfig : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("流量渠道", flowConfig.getFlowChannel());
            map.put("流量授信日限额", flowConfig.getCreditDayAmt());
            map.put("流量放款日限额", flowConfig.getLoanDayAmt());
            map.put("第一绑卡渠道", flowConfig.getFirstProtocolChannel());
            map.put("第二绑卡渠道", flowConfig.getSecondProtocolChannel());
            map.put("启用状态 1 DISABLE 2 ENABLE", flowConfig.getEnabled());
            map.put("备注", flowConfig.getRemark());
            map.put("乐观锁", flowConfig.getRevision());
            map.put("创建人", flowConfig.getCreatedBy());
            map.put("创建时间", flowConfig.getCreatedTime());
            map.put("更新人", flowConfig.getUpdatedBy());
            map.put("更新时间", flowConfig.getUpdatedTime());
            map.put("资产方简称", flowConfig.getFlowNameShort());
            map.put("资产方主体全称", flowConfig.getFlowName());
            map.put("资方简介", flowConfig.getFlowDesc());
            map.put("联系人", flowConfig.getContactPerson());
            map.put("联系电话", flowConfig.getContactPhone());
            map.put("邮箱地址", flowConfig.getEmailAddress());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}