<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="project.mapper.FlowConfigMapper">
    <resultMap id="BaseResultMap" type="project.domain.FlowConfig">
        <id column="id" property="id"/>
        <result column="flow_channel" property="flowChannel"/>
        <result column="credit_day_amt" property="creditDayAmt"/>
        <result column="loan_day_amt" property="loanDayAmt"/>
        <result column="first_protocol_channel" property="firstProtocolChannel"/>
        <result column="second_protocol_channel" property="secondProtocolChannel"/>
        <result column="enabled" property="enabled"/>
        <result column="remark" property="remark"/>
        <result column="revision" property="revision"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="flow_name_short" property="flowNameShort"/>
        <result column="flow_name" property="flowName"/>
        <result column="flow_desc" property="flowDesc"/>
        <result column="contact_person" property="contactPerson"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="email_address" property="emailAddress"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, flow_channel, credit_day_amt, loan_day_amt, first_protocol_channel, second_protocol_channel, enabled, remark, revision, created_by, created_time, updated_by, updated_time, flow_name_short, flow_name, flow_desc, contact_person, contact_phone, email_address
    </sql>

    <select id="findAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from flow_config
        <where>
        </where>
        order by id desc
    </select>
</mapper>