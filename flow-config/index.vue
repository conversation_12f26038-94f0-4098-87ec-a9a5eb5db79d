<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">流量渠道</label>
        <el-input v-model="query.flowChannel" clearable placeholder="流量渠道" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="主键" prop="id">
            <el-input v-model="form.id" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="流量渠道" prop="flowChannel">
            <el-select v-model="form.flowChannel" filterable placeholder="请选择">
              <el-option
                v-for="item in dict.job_status"
                :key="item.id"
                :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="流量授信日限额" prop="creditDayAmt">
            <el-input v-model="form.creditDayAmt" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="流量放款日限额" prop="loanDayAmt">
            <el-input v-model="form.loanDayAmt" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="第一绑卡渠道">
            <el-input v-model="form.firstProtocolChannel" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="第二绑卡渠道">
            <el-input v-model="form.secondProtocolChannel" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="启用状态 1 DISABLE 2 ENABLE" prop="enabled">
            <el-input v-model="form.enabled" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="备注">
            <el-input v-model="form.remark" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="乐观锁">
            <el-input v-model="form.revision" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="创建人">
            <el-input v-model="form.createdBy" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="创建时间">
            <el-input v-model="form.createdTime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="更新人">
            <el-input v-model="form.updatedBy" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="更新时间">
            <el-input v-model="form.updatedTime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="资产方简称">
            <el-input v-model="form.flowNameShort" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="资产方主体全称">
            <el-input v-model="form.flowName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="资方简介">
            <el-input v-model="form.flowDesc" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="联系人">
            <el-input v-model="form.contactPerson" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="联系电话">
            <el-input v-model="form.contactPhone" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="邮箱地址">
            <el-input v-model="form.emailAddress" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="主键" />
        <el-table-column prop="flowChannel" label="流量渠道">
          <template slot-scope="scope">
            {{ dict.label.job_status[scope.row.flowChannel] }}
          </template>
        </el-table-column>
        <el-table-column prop="creditDayAmt" label="流量授信日限额" />
        <el-table-column prop="loanDayAmt" label="流量放款日限额" />
        <el-table-column prop="firstProtocolChannel" label="第一绑卡渠道" />
        <el-table-column prop="secondProtocolChannel" label="第二绑卡渠道" />
        <el-table-column prop="enabled" label="启用状态 1 DISABLE 2 ENABLE" />
        <el-table-column prop="remark" label="备注" />
        <el-table-column prop="revision" label="乐观锁" />
        <el-table-column prop="createdBy" label="创建人" />
        <el-table-column prop="createdTime" label="创建时间" />
        <el-table-column prop="updatedBy" label="更新人" />
        <el-table-column prop="updatedTime" label="更新时间" />
        <el-table-column prop="flowNameShort" label="资产方简称" />
        <el-table-column prop="flowName" label="资产方主体全称" />
        <el-table-column prop="flowDesc" label="资方简介" />
        <el-table-column prop="contactPerson" label="联系人" />
        <el-table-column prop="contactPhone" label="联系电话" />
        <el-table-column prop="emailAddress" label="邮箱地址" />
        <el-table-column v-if="checkPer(['admin','flowConfig:edit','flowConfig:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudFlowConfig from '@/api/flowConfig'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, flowChannel: null, creditDayAmt: null, loanDayAmt: null, firstProtocolChannel: null, secondProtocolChannel: null, enabled: null, remark: null, revision: null, createdBy: null, createdTime: null, updatedBy: null, updatedTime: null, flowNameShort: null, flowName: null, flowDesc: null, contactPerson: null, contactPhone: null, emailAddress: null }
export default {
  name: 'FlowConfig',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['job_status'],
  cruds() {
    return CRUD({ title: '资产控制', url: 'api/flowConfig', idField: 'id', sort: 'id,desc', crudMethod: { ...crudFlowConfig }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'flowConfig:add'],
        edit: ['admin', 'flowConfig:edit'],
        del: ['admin', 'flowConfig:del']
      },
      rules: {
        id: [
          { required: true, message: '主键不能为空', trigger: 'blur' }
        ],
        flowChannel: [
          { required: true, message: '流量渠道不能为空', trigger: 'blur' }
        ],
        creditDayAmt: [
          { required: true, message: '流量授信日限额不能为空', trigger: 'blur' }
        ],
        loanDayAmt: [
          { required: true, message: '流量放款日限额不能为空', trigger: 'blur' }
        ],
        enabled: [
          { required: true, message: '启用状态 1 DISABLE 2 ENABLE不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'flowChannel', display_name: '流量渠道' }
      ]
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
