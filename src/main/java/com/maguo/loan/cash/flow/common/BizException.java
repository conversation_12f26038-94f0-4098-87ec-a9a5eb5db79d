package com.maguo.loan.cash.flow.common;

/**
 * <AUTHOR>
 * @date 2023/10/10
 */
public class BizException extends RuntimeException {
    private final ResultCode resultCode;

    public BizException(String message, ResultCode resultCode) {
        super(message);
        this.resultCode = resultCode;
    }

    public BizException(ResultCode rc) {
        super(rc.getMsg());
        this.resultCode = rc;
    }

    public ResultCode getResultCode() {
        return resultCode;
    }
}
