package com.maguo.loan.cash.flow.common;


import com.maguo.loan.cash.flow.enums.FlowChannel;

/**
 * 回调异常
 *
 * <AUTHOR>
 */
public class CallBackException extends RuntimeException {

    private final FlowChannel flowChannel;


    public CallBackException(FlowChannel flowChannel, String message) {
        super(flowChannel + ":" + message);
        this.flowChannel = flowChannel;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }
}
