package com.maguo.loan.cash.flow.common;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

public class RequestUriLogFilter extends HttpFilter {

    private static final Logger logger = LoggerFactory.getLogger(RequestUriLogFilter.class);

    @Override
    protected void doFilter(HttpServletRequest req, HttpServletResponse res, FilterChain chain) throws IOException, ServletException {
        String requestUri = req.getRequestURI();
        String requestMethod = req.getMethod();
        logger.info("request in [{} {}]", requestMethod, requestUri);
        chain.doFilter(req, res);
        logger.info("request out [{} {}]", requestMethod, requestUri);
    }
}
