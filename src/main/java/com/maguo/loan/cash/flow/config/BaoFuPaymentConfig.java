package com.maguo.loan.cash.flow.config;


import com.zsjz.third.part.LocalCachedMerchantConfigFetcher;
import com.zsjz.third.part.baofoo.BaoFuBindCardService;
import com.zsjz.third.part.baofoo.BaoFuBindConfirmService;
import com.zsjz.third.part.baofoo.BaoFuChargeQueryService;
import com.zsjz.third.part.baofoo.BaoFuChargeService;
import com.zsjz.third.part.baofoo.settlement.BaoFuAccountRechargeQueryService;
import com.zsjz.third.part.baofoo.settlement.BaoFuAccountRechargeService;
import com.zsjz.third.part.baofoo.settlement.BaoFuTransferQueryService;
import com.zsjz.third.part.baofoo.settlement.BaoFuTransferService;
import com.zsjz.third.part.baofoo.settlement.BaoFuWithdrawalQueryService;
import com.zsjz.third.part.baofoo.settlement.BaoFuWithdrawalService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class BaoFuPaymentConfig {

    // 初始化各个服务 Bean
    @Bean
    public BaoFuBindCardService baofuBindCardService() {
        return new BaoFuBindCardService();
    }

    @Bean
    public BaoFuBindConfirmService baofuBindConfirmService() {
        return new BaoFuBindConfirmService();
    }

    @Bean
    public BaoFuChargeService baofuChargeService() {
        return new BaoFuChargeService();
    }

    @Bean
    public BaoFuChargeQueryService baofuChargeQueryService() {
        return new BaoFuChargeQueryService();
    }

    @Bean
    public LocalCachedMerchantConfigFetcher localCachedMerchantConfigFetcher(){
        return new LocalCachedMerchantConfigFetcher();
    }

    @Bean
    public BaoFuAccountRechargeQueryService baoFuAccountRechargeQueryService(){
        return new BaoFuAccountRechargeQueryService();
    }

    @Bean
    public BaoFuAccountRechargeService baoFuAccountRechargeService(){
        return new BaoFuAccountRechargeService();
    }

    @Bean
    public BaoFuTransferQueryService baoFuTransferQueryService(){
        return new BaoFuTransferQueryService();
    }

    @Bean
    public BaoFuTransferService baoFuTransferService(){
        return new BaoFuTransferService();
    }

    @Bean
    public BaoFuWithdrawalQueryService baoFuWithdrawalQueryService(){
        return new BaoFuWithdrawalQueryService();
    }

    @Bean
    public BaoFuWithdrawalService baoFuWithdrawalService(){
        return new BaoFuWithdrawalService();
    }




}




