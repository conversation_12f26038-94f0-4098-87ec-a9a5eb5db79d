package com.maguo.loan.cash.flow.config;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigChangeListener;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.zsjz.third.part.baofoo.settlement.BaoFuSettlementMerchantConfig;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Configuration
@Slf4j
public class BaoFuSettlementMerchantConfigManage {
    private static final String CONFIG_KEY = "settlement.baofu.merchants.config";

    // 当前使用的计算商户号
    private static final String ACTIVE_MERCHANT_KEY = "settlement.baofu.active.merchant";
    @Getter
    private String activeMerchantId;


    private Map<String,BaoFuSettlementMerchantConfig> merchantConfig = new ConcurrentHashMap<>();


    private final Gson gson = new Gson();

    @PostConstruct
    public void init() {
        Config apolloConfig = ConfigService.getAppConfig();
        // 加载商户配置
        loadConfig(apolloConfig);
        // 注册配置变更监听器
        apolloConfig.addChangeListener(new ConfigChangeListener() {
            @Override
            public void onChange(ConfigChangeEvent event) {
                // 监听变更
                if (event.changedKeys().contains(CONFIG_KEY) ||  event.changedKeys().contains(ACTIVE_MERCHANT_KEY)) {
                    loadConfig(apolloConfig);
                }
            }
        });
    }

    private void loadConfig(Config apolloConfig) {
        String activeMerchantId = apolloConfig.getProperty(ACTIVE_MERCHANT_KEY, "");
        String configJson = apolloConfig.getProperty(CONFIG_KEY, "{}");
        Map<String, BaoFuSettlementMerchantConfig> newConfig = gson.fromJson(
            configJson,
            new TypeToken<Map<String, BaoFuSettlementMerchantConfig>>(){}.getType()
        );
        this.activeMerchantId=activeMerchantId;
        merchantConfig.clear();
        merchantConfig.putAll(newConfig);
    }


    public BaoFuSettlementMerchantConfig getMerchantConfig(String merchantId) {
        return merchantConfig.get(merchantId);
    }

    public BaoFuSettlementMerchantConfig getActiveMerchantConfig() {
        return merchantConfig.get(activeMerchantId);
    }


}
