package com.maguo.loan.cash.flow.config;

import org.springframework.context.annotation.Configuration;

/**
 * 宝付配置
 */
@Configuration
public class BaofooConfig {
    /**
     * 平台绑卡-鼎发顺, 二级商户号。
     * 绑卡主体切到 益通祥，废弃
     */
    //@Value("${bf.sign.merchantNo}")
    private String merchantNo;

    /**
     * 亲家临商资方绑卡,我司拿 鼎丰融担 去宝付绑卡
     * 亲家临商鼎丰融担, 二级商户号
     */
    //@Value("${bf.dfrd.merchantNo}")
    private String dfrdMerchantNo;

    /**
     * 亲家亿联F003, 我司拿 鼎丰融担 代资方绑卡
     * 亲家亿联鼎丰融担, 二级商户号
     */
    //@Value("${bf.qjyl.charge.shareInfo.yndfrd.terminal}")
    private String terminal;


    /**
     * 融担费&咨询费 代偿前扣款商户类型
     */
   // @Value("${bf.guarantee_consult.charge.accountType}")
    private String guaranteeConsultAccountType;

    /**
     * 融担费&咨询费 代偿前扣款商户号
     */
   // @Value("${bf.guarantee_consult.charge.accountMemberId}")
    private String guaranteeConsultAccountMemberId;

    /**
     * 润楼长银分润-鑫汇融担 线上还款12%咨询费扣款宝付户
     * 扣完后清分12%咨询费至益通祥(1276522)
     */
    //@Value("${bf.cycfcfl.xh.consult.charge.accountMemberId}")
    private String cycfcFlXhConsultWithholdTypeMemberId;

    //@Deprecated(since = "平台绑卡主体切成益通祥", forRemoval = true)
    public String getMerchantNo() {
        return merchantNo;
    }

    public String getDfrdMerchantNo() {
        return dfrdMerchantNo;
    }


    public String getTerminal() {
        return terminal;
    }


    public String getGuaranteeConsultAccountMemberId() {
        return guaranteeConsultAccountMemberId;
    }


    public String getGuaranteeConsultAccountType() {
        return guaranteeConsultAccountType;
    }


    public String getCycfcFlXhConsultWithholdTypeMemberId() {
        return cycfcFlXhConsultWithholdTypeMemberId;
    }
}
