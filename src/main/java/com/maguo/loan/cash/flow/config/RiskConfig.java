package com.maguo.loan.cash.flow.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 风控配置
 *
 * <AUTHOR>
 */
@Configuration
@Data
public class RiskConfig {
    /**
     * 放款风控流量渠道
     */
    @Value("${risk.loan.flowChannel}")
    private String riskLoanFlowChannel;
    /**
     * 拍拍授信风控请求决策流号
     */
    @Value("${risk.credit.apply.ppd.serialNumber}")
    private String creditApplyPpdSerialNumber;
    /**
     * 拍拍授信风控请求密钥
     */
    @Value("${risk.credit.apply.ppd.systemKey}")
    private String creditApplyPpdSystemKey;
    /**
     * 绿信授信风控请求决策流号
     */
    @Value("${risk.credit.apply.lvxin.serialNumber}")
    private String creditApplyLvxinSerialNumber;
    /**
     * 绿信授信风控请求密钥
     */
    @Value("${risk.credit.apply.lvxin.systemKey}")
    private String creditApplyLvxinSystemKey;
    /**
     * 拍拍放款风控请求决策流号
     */
    @Value("${risk.loan.apply.ppd.serialNumber}")
    private String loanApplyPpdSerialNumber;
    /**
     * 拍拍放款风控请求密钥
     */
    @Value("${risk.loan.apply.ppd.systemKey}")
    private String loanApplyPpdSystemKey;
    /**
     * 内部风控开关
     */
    @Value("${risk.enable}")
    private Boolean riskEnable;
    /**
     * 风控请求地址
     */
    @Value("${risk.url}")
    private String riskUrl;

    public String getRiskLoanFlowChannel() {
        return riskLoanFlowChannel;
    }

    public void setRiskLoanFlowChannel(String riskLoanFlowChannel) {
        this.riskLoanFlowChannel = riskLoanFlowChannel;
    }

    public String getCreditApplyPpdSerialNumber() {
        return creditApplyPpdSerialNumber;
    }

    public void setCreditApplyPpdSerialNumber(String creditApplyPpdSerialNumber) {
        this.creditApplyPpdSerialNumber = creditApplyPpdSerialNumber;
    }

    public String getCreditApplyPpdSystemKey() {
        return creditApplyPpdSystemKey;
    }

    public void setCreditApplyPpdSystemKey(String creditApplyPpdSystemKey) {
        this.creditApplyPpdSystemKey = creditApplyPpdSystemKey;
    }

    public String getCreditApplyLvxinSerialNumber() {
        return creditApplyLvxinSerialNumber;
    }

    public void setCreditApplyLvxinSerialNumber(String creditApplyLvxinSerialNumber) {
        this.creditApplyLvxinSerialNumber = creditApplyLvxinSerialNumber;
    }

    public String getCreditApplyLvxinSystemKey() {
        return creditApplyLvxinSystemKey;
    }

    public void setCreditApplyLvxinSystemKey(String creditApplyLvxinSystemKey) {
        this.creditApplyLvxinSystemKey = creditApplyLvxinSystemKey;
    }

    public String getLoanApplyPpdSerialNumber() {
        return loanApplyPpdSerialNumber;
    }

    public void setLoanApplyPpdSerialNumber(String loanApplyPpdSerialNumber) {
        this.loanApplyPpdSerialNumber = loanApplyPpdSerialNumber;
    }

    public String getLoanApplyPpdSystemKey() {
        return loanApplyPpdSystemKey;
    }

    public void setLoanApplyPpdSystemKey(String loanApplyPpdSystemKey) {
        this.loanApplyPpdSystemKey = loanApplyPpdSystemKey;
    }

    public Boolean getRiskEnable() {
        return riskEnable;
    }

    public void setRiskEnable(Boolean riskEnable) {
        this.riskEnable = riskEnable;
    }

    public String getRiskUrl() {
        return riskUrl;
    }

    public void setRiskUrl(String riskUrl) {
        this.riskUrl = riskUrl;
    }
}
