package com.maguo.loan.cash.flow.config;

import com.maguo.loan.cash.flow.service.push.impl.PushToMkServiceImpl;
import com.maguo.loan.cash.flow.util.TraceIdGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 */


@Configuration
@EnableAsync
public class ThreadPoolConfig {

    private static final Logger log = LoggerFactory.getLogger(ThreadPoolConfig.class);

    /**
     * 核心线程数
     */
    @Value("${thread-pool.core-pool-size:50}")
    private int corePoolSize;


    /**
     * 最大线程数
     * 决
     */
    @Value("${thread-pool.max-pool-size:100}")
    private int maxPoolSize;

    /**
     * 缓冲队列
     */
    @Value("${thread-pool.queue-capacity:100}")
    private int queueCapacity;

    /**
     * 允许线程的空闲时间
     */
    @Value("${thread-pool.keep-alive-seconds:60}")
    private int keepAliveSeconds;

    /**
     * 等待所有任务结束后再关闭线程池
     */
    @Value("${thread-pool.wait-tasks-complete-shutdown:true}")
    private boolean waitForTasksToCompleteOnShutdown;

    /**
     * 线程前缀
     */
    @Value("${thread-pool.thread-name-prefix:threadPool-}")
    private String threadNamePrefix;

    private static final String SHM_PUSH = "shmPush";


    @Bean("shmPush")
    public Executor shmPush() {
        ThreadPoolTaskExecutor threadPoolExecutor = new ThreadPoolTaskExecutor();
        threadPoolExecutor.setThreadNamePrefix(threadNamePrefix + SHM_PUSH);
        threadPoolExecutor.setCorePoolSize(corePoolSize);
        threadPoolExecutor.setMaxPoolSize(maxPoolSize);
        threadPoolExecutor.setQueueCapacity(queueCapacity);
        threadPoolExecutor.setKeepAliveSeconds(keepAliveSeconds);
        threadPoolExecutor.setWaitForTasksToCompleteOnShutdown(waitForTasksToCompleteOnShutdown);
        threadPoolExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        threadPoolExecutor.setTaskDecorator(new TraceIdGenerator());
        threadPoolExecutor.initialize();
        log.info("ThreadPoolTaskExecutor Initialized -  CorePoolSize:{}, MaxPoolSize: {}, QueueCapacity: {}",
            threadPoolExecutor.getCorePoolSize(),
            threadPoolExecutor.getMaxPoolSize(),
            threadPoolExecutor.getQueueCapacity());
        return threadPoolExecutor;
    }


}
