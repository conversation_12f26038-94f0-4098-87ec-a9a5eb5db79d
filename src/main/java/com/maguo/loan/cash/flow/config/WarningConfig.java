package com.maguo.loan.cash.flow.config;

import brave.Tracer;

import com.maguo.loan.cash.flow.service.WarningService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.lang.Nullable;

@Configuration
public class WarningConfig {

    @Value("${warning.key.default}")
    private String defaultWarningKey;

    @Value("${warning.state.key.default}")
    private String stateWarningKey;
    @Value("${warning.serviceData.key.default}")
    private String serviceDataWarningKey;
    @Bean
    @Primary
    public WarningService warningService(@Nullable Tracer tracer) {
        return new WarningService(defaultWarningKey, tracer);
    }

    @Bean
    public WarningService warningStateService(@Nullable Tracer tracer) {
        return new WarningService(stateWarningKey, tracer);
    }

    @Bean
    public WarningService warningServiceDataService(@Nullable Tracer tracer) {
        return new WarningService(serviceData<PERSON><PERSON><PERSON><PERSON><PERSON>, tracer);
    }
}
