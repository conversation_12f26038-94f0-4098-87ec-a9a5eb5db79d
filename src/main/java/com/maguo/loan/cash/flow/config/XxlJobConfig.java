package com.maguo.loan.cash.flow.config;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class XxlJobConfig {

    private static final Logger log = LoggerFactory.getLogger(XxlJobConfig.class);

    @Value("${xxl.job.admin.addresses}")
    private String adminAddresses;
    @Value("${xxl.job.executor.appname:}")
    private String appName;
    @Value("${xxl.job.executor.ip:}")
    private String ip;
    @Value("${xxl.job.executor.port:-1}")
    private int port;
    @Value("${xxl.job.accessToken:}")
    private String accessToken;
    @Value("${xxl.job.executor.logpath:}")
    private String logPath;
    @Value("${xxl.job.executor.logretentiondays:30}")
    private int logRetentionDays;

    @Bean(initMethod = "start", destroyMethod = "destroy")
    public XxlJobSpringExecutor xxlJobExecutor() {
       log.info(">>>>>>>>>>> xxl-job config init.");
       XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
       xxlJobSpringExecutor.setAdminAddresses(adminAddresses);
       xxlJobSpringExecutor.setAppName(appName);
       xxlJobSpringExecutor.setIp(ip);
       xxlJobSpringExecutor.setPort(port);
       xxlJobSpringExecutor.setAccessToken(accessToken);
       xxlJobSpringExecutor.setLogPath(logPath);
       xxlJobSpringExecutor.setLogRetentionDays(logRetentionDays);
       return xxlJobSpringExecutor;
    }
}
