package com.maguo.loan.cash.flow.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.time.LocalDateTime;

@Entity
@Table(name = "bank_bin_info")
public class BankBinInfo {
    @Id
    private Integer id;

    private String cardBin;

    private Integer cardLength;

    private Integer cardBinLength;

    private String bankUnionPayCode;

    private String bankUnionPayName;

    private String isCredit;

    private String orderNo;

    private String bankCode;

    /**
     * 银行缩写
     */
    private String bankAbbr;

    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCardBin() {
        return cardBin;
    }

    public void setCardBin(String cardBin) {
        this.cardBin = cardBin == null ? null : cardBin.trim();
    }

    public Integer getCardLength() {
        return cardLength;
    }

    public void setCardLength(Integer cardLength) {
        this.cardLength = cardLength;
    }

    public Integer getCardBinLength() {
        return cardBinLength;
    }

    public void setCardBinLength(Integer cardBinLength) {
        this.cardBinLength = cardBinLength;
    }

    public String getBankUnionPayCode() {
        return bankUnionPayCode;
    }

    public void setBankUnionPayCode(String bankUnionPayCode) {
        this.bankUnionPayCode = bankUnionPayCode;
    }

    public String getBankUnionPayName() {
        return bankUnionPayName;
    }

    public void setBankUnionPayName(String bankUnionPayName) {
        this.bankUnionPayName = bankUnionPayName;
    }

    public String getIsCredit() {
        return isCredit;
    }

    public void setIsCredit(String isCredit) {
        this.isCredit = isCredit == null ? null : isCredit.trim();
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode == null ? null : bankCode.trim();
    }

    public String getBankAbbr() {
        return bankAbbr;
    }

    public void setBankAbbr(String bankAbbr) {
        this.bankAbbr = bankAbbr;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }
}
