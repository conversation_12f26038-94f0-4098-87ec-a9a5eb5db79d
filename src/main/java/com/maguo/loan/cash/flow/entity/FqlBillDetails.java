package com.maguo.loan.cash.flow.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;

import java.math.BigDecimal;

/**
 * 分期乐还款账单明细,如果是提前结清,会有多条
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "fql_bill_details")
public class FqlBillDetails extends BaseEntity {

    @Override
    protected String prefix() {
        return "FQL";
    }

    /**
     * 账单维度的还款总额（单位：分）
     */
    private BigDecimal rpyAmt;
    /**
     * 实还本金（单位：分）
     */
    private BigDecimal rpyPrincipal;
    /**
     * 实还利息（单位：分）
     */
    private BigDecimal rpyFeeAmt;
    /**
     * 实还罚息（单位：分）
     */
    private BigDecimal rpyMuclt;
    /**
     * 还款期数
     */
    private Integer rpyTerm;
    /**
     * 分期乐代扣明细id
     */
    private String fqlWithholdDetailId;

    public BigDecimal getRpyAmt() {
        return rpyAmt;
    }

    public void setRpyAmt(BigDecimal rpyAmt) {
        this.rpyAmt = rpyAmt;
    }

    public BigDecimal getRpyPrincipal() {
        return rpyPrincipal;
    }

    public void setRpyPrincipal(BigDecimal rpyPrincipal) {
        this.rpyPrincipal = rpyPrincipal;
    }

    public BigDecimal getRpyFeeAmt() {
        return rpyFeeAmt;
    }

    public void setRpyFeeAmt(BigDecimal rpyFeeAmt) {
        this.rpyFeeAmt = rpyFeeAmt;
    }

    public BigDecimal getRpyMuclt() {
        return rpyMuclt;
    }

    public void setRpyMuclt(BigDecimal rpyMuclt) {
        this.rpyMuclt = rpyMuclt;
    }

    public Integer getRpyTerm() {
        return rpyTerm;
    }

    public void setRpyTerm(Integer rpyTerm) {
        this.rpyTerm = rpyTerm;
    }

    public String getFqlWithholdDetailId() {
        return fqlWithholdDetailId;
    }

    public void setFqlWithholdDetailId(String fqlWithholdDetailId) {
        this.fqlWithholdDetailId = fqlWithholdDetailId;
    }
}
