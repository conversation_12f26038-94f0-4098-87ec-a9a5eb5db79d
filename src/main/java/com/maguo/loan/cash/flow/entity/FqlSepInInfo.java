package com.maguo.loan.cash.flow.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;

import java.math.BigDecimal;

/**
 * 分期乐分账信息
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "fql_sep_in_info")
public class FqlSepInInfo extends BaseEntity {

    @Override
    protected String prefix() {
        return "FQL";
    }

    /**
     * 是否为代扣商户号，1-是， 0-否
     */
    private String withholdMerchants;
    /**
     * 机构类型 ：1：主扣款商户号， 2：被分账商户号
     */
    private String orgType;
    /**
     * 分账总金额 ,（单位 分）
     */
    private BigDecimal amt;
    /**
     * （分账明细(可能有多条明细))
     */
    private String detail;
    /**
     * 机构编码
     */
    private String sepMerchCode;
    /**
     * type:被分账对象 （收款主体类型） 1：资方 2：担保方/其他
     */
    private String type;
    /**
     * 账号所属银行
     */
    private String sepBankId;
    /**
     * 入账商户号
     */
    private String account;
    /**
     * 分期乐还款申请记录id
     */
    private String fqlRepayApplyRecordId;

    public String getWithholdMerchants() {
        return withholdMerchants;
    }

    public void setWithholdMerchants(String withholdMerchants) {
        this.withholdMerchants = withholdMerchants;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public BigDecimal getAmt() {
        return amt;
    }

    public void setAmt(BigDecimal amt) {
        this.amt = amt;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getSepMerchCode() {
        return sepMerchCode;
    }

    public void setSepMerchCode(String sepMerchCode) {
        this.sepMerchCode = sepMerchCode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSepBankId() {
        return sepBankId;
    }

    public void setSepBankId(String sepBankId) {
        this.sepBankId = sepBankId;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getFqlRepayApplyRecordId() {
        return fqlRepayApplyRecordId;
    }

    public void setFqlRepayApplyRecordId(String fqlRepayApplyRecordId) {
        this.fqlRepayApplyRecordId = fqlRepayApplyRecordId;
    }
}
