package com.maguo.loan.cash.flow.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;

import java.math.BigDecimal;

/**
 * 分期乐出账信息
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "fql_sep_out_info")
public class FqlSepOutInfo extends BaseEntity {

    @Override
    protected String prefix() {
        return "FQL";
    }

    /**
     * 出账金额（单位 分）
     */
    private BigDecimal amt;
    /**
     * 出账主体"(1: 用户账户出账 , 2.补差账户出账(即营销金额))"
     */
    private String type;
    /**
     * 出账账户
     */
    private String account;
    /**
     * 分期乐还款申请记录id
     */
    private String fqlRepayApplyRecordId;

    public BigDecimal getAmt() {
        return amt;
    }

    public void setAmt(BigDecimal amt) {
        this.amt = amt;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getFqlRepayApplyRecordId() {
        return fqlRepayApplyRecordId;
    }

    public void setFqlRepayApplyRecordId(String fqlRepayApplyRecordId) {
        this.fqlRepayApplyRecordId = fqlRepayApplyRecordId;
    }
}
