package com.maguo.loan.cash.flow.entity;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.LoanPurpose;
import com.maguo.loan.cash.flow.enums.ProcessState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 放款记录表
 */
@Entity
@Table(name = "loan_record")
public class LoanRecord extends BaseEntity {

    /**
     * 借据id
     */
    private String loanId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 订单id
     */
    private String orderId;
    /**
     * 授信id
     */
    private String creditId;

    /**
     * 申请时间
     */
    private LocalDateTime applyTime;
    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 期数
     */
    private Integer periods;

    /**
     * 放款状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessState loanState;

    /**
     * 流量渠道
     */
    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;
    /**
     * 资金渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel bankChannel;

    /**
     * 融担公司
     */
    @Enumerated(EnumType.STRING)
    private GuaranteeCompany guaranteeCompany;

    /**
     * 资金编号
     */
    private String loanNo;

    /**
     * 对客利率（金融）
     */
    private BigDecimal irrRate;
    /**
     * 资方利率
     */
    private BigDecimal bankRate;
    /**
     * 放款成功时间
     */
    private LocalDateTime loanTime;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 借款合同编号
     */
    private String loanContractNo;

    /**
     * 用途
     */
    @Enumerated(EnumType.STRING)
    private LoanPurpose loanPurpose;

    @Override
    protected String prefix() {
        return "LOR";
    }


    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getCreditId() {
        return creditId;
    }

    public void setCreditId(String creditId) {
        this.creditId = creditId;
    }

    public LocalDateTime getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(LocalDateTime applyTime) {
        this.applyTime = applyTime;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getPeriods() {
        return periods;
    }

    public void setPeriods(Integer periods) {
        this.periods = periods;
    }

    public ProcessState getLoanState() {
        return loanState;
    }

    public void setLoanState(ProcessState loanState) {
        this.loanState = loanState;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public GuaranteeCompany getGuaranteeCompany() {
        return guaranteeCompany;
    }

    public void setGuaranteeCompany(GuaranteeCompany guaranteeCompany) {
        this.guaranteeCompany = guaranteeCompany;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public BigDecimal getIrrRate() {
        return irrRate;
    }

    public void setIrrRate(BigDecimal irrRate) {
        this.irrRate = irrRate;
    }

    public BigDecimal getBankRate() {
        return bankRate;
    }

    public void setBankRate(BigDecimal bankRate) {
        this.bankRate = bankRate;
    }

    public LocalDateTime getLoanTime() {
        return loanTime;
    }

    public void setLoanTime(LocalDateTime loanTime) {
        this.loanTime = loanTime;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public String getLoanContractNo() {
        return loanContractNo;
    }

    public void setLoanContractNo(String loanContractNo) {
        this.loanContractNo = loanContractNo;
    }

    public LoanPurpose getLoanPurpose() {
        return loanPurpose;
    }

    public void setLoanPurpose(LoanPurpose loanPurpose) {
        this.loanPurpose = loanPurpose;
    }
}
