package com.maguo.loan.cash.flow.entity;


import com.maguo.loan.cash.flow.enums.ApplicationSource;
import com.maguo.loan.cash.flow.enums.OcrClientType;
import com.maguo.loan.cash.flow.enums.ProcessState;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;

@Entity
@Table(name = "ocr_face")
public class OcrFace extends BaseEntity {

    private String userId;


    private String flowChannel;

    private String ossBucket;

    private String dataUrl;


    @Enumerated(EnumType.STRING)
    private ProcessState status;

    @Enumerated(EnumType.STRING)
    private ProcessState hackStatus;


    @Column
    private int code;

    @Column
    private int hackCode;
    @Enumerated(EnumType.STRING)
    private OcrClientType clientId;


    private String clientNo;

    private String hackClientNo;

    private BigDecimal score;

    private BigDecimal hackScore;

    private String charge;

    private String hackCharge;


    private String remark;

    @Enumerated(EnumType.STRING)
    private ApplicationSource applicationSource;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(String flowChannel) {
        this.flowChannel = flowChannel;
    }

    public String getDataUrl() {
        return dataUrl;
    }

    public void setDataUrl(String dataUrl) {
        this.dataUrl = dataUrl;
    }

    public ProcessState getStatus() {
        return status;
    }

    public void setStatus(ProcessState status) {
        this.status = status;
    }

    public ProcessState getHackStatus() {
        return hackStatus;
    }

    public void setHackStatus(ProcessState hackStatus) {
        this.hackStatus = hackStatus;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public int getHackCode() {
        return hackCode;
    }

    public void setHackCode(int hackCode) {
        this.hackCode = hackCode;
    }

    public OcrClientType getClientId() {
        return clientId;
    }

    public void setClientId(OcrClientType clientId) {
        this.clientId = clientId;
    }

    public String getClientNo() {
        return clientNo;
    }

    public void setClientNo(String clientNo) {
        this.clientNo = clientNo;
    }

    public String getHackClientNo() {
        return hackClientNo;
    }

    public void setHackClientNo(String hackClientNo) {
        this.hackClientNo = hackClientNo;
    }

    public BigDecimal getScore() {
        return score;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }

    public BigDecimal getHackScore() {
        return hackScore;
    }

    public void setHackScore(BigDecimal hackScore) {
        this.hackScore = hackScore;
    }

    public String getCharge() {
        return charge;
    }

    public void setCharge(String charge) {
        this.charge = charge;
    }

    public String getHackCharge() {
        return hackCharge;
    }

    public void setHackCharge(String hackCharge) {
        this.hackCharge = hackCharge;
    }


    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getOssBucket() {
        return ossBucket;
    }

    public void setOssBucket(String ossBucket) {
        this.ossBucket = ossBucket;
    }

    public ApplicationSource getApplicationSource() {
        return applicationSource;
    }

    public void setApplicationSource(ApplicationSource applicationSource) {
        this.applicationSource = applicationSource;
    }

    @Override
    protected String prefix() {
        return "OF";
    }
}
