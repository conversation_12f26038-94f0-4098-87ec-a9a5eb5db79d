package com.maguo.loan.cash.flow.entity;


import com.maguo.loan.cash.flow.enums.PaymentChannel;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RightsPayType;
import com.maguo.loan.cash.flow.enums.RightsSupplier;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
@Entity
@Table(name = "rights_order")
public class RightsOrder extends BaseEntity {
    @Enumerated(EnumType.STRING)
    private RightsPayType rightsPayType;

    private String userId;
    /**
     * 订单id
     */
    private String orderId;
    /**
     * 借据
     */
    private String loanId;

    /**
     * 来源业务id
     */
    private String sourceId;

    /**
     * 外部订单编号
     */
    private String outOrderNo;
    /**
     * 权益订单id
     */
    private String billRightsOrderId;

    /**
     * 支付金额
     */
    private BigDecimal payAmount;
    /**
     * 支付时间
     */
    private LocalDateTime payTime;
    /**
     * 状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessState state;
    /**
     * 支付渠道
     */
    @Enumerated(EnumType.STRING)
    private PaymentChannel payChannel;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 姓名
     */
    private String realName;
    /**
     * 权益包
     */
    private String packageId;
    /**
     * 权益供应商
     */
    @Enumerated(EnumType.STRING)
    private RightsSupplier rightsSupplier;

    public RightsPayType getRightsPayType() {
        return rightsPayType;
    }

    public void setRightsPayType(RightsPayType rightsPayType) {
        this.rightsPayType = rightsPayType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public LocalDateTime getPayTime() {
        return payTime;
    }

    public void setPayTime(LocalDateTime payTime) {
        this.payTime = payTime;
    }


    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getPackageId() {
        return packageId;
    }

    public void setPackageId(String packageId) {
        this.packageId = packageId;
    }

    public ProcessState getState() {
        return state;
    }

    public void setState(ProcessState state) {
        this.state = state;
    }

    public PaymentChannel getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(PaymentChannel payChannel) {
        this.payChannel = payChannel;
    }

    public RightsSupplier getRightsSupplier() {
        return rightsSupplier;
    }

    public void setRightsSupplier(RightsSupplier rightsSupplier) {
        this.rightsSupplier = rightsSupplier;
    }

    @Override
    protected String prefix() {
        return "RO";
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getOutOrderNo() {
        return outOrderNo;
    }

    public void setOutOrderNo(String outOrderNo) {
        this.outOrderNo = outOrderNo;
    }

    public String getBillRightsOrderId() {
        return billRightsOrderId;
    }

    public void setBillRightsOrderId(String billRightsOrderId) {
        this.billRightsOrderId = billRightsOrderId;
    }
}
