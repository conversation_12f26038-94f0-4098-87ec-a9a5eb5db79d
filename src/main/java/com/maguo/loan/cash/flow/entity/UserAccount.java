package com.maguo.loan.cash.flow.entity;


import com.maguo.loan.cash.flow.enums.LoginChannel;
import com.maguo.loan.cash.flow.enums.MarketingChannel;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.time.LocalDateTime;

/**
 * @Classname UserAccount
 * @Description 用户账户信息
 * @Date 2023/10/9 17:37
 * @Created by gale
 */

@Entity
@Table(name = "user_account")
public class UserAccount extends BaseEntity {


    /**
     * 手机号
     */
    private String mobile;
    /**
     * 用户
     */
    private String userId;
    /**
     * 密码
     */
    private String password;
    /**
     * 是否完成首次密码设置
     */
    private String pwdSettingState;
    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 外部用户id
     */
    private String outUserId;
    /**
     * token有效期
     */
    private LocalDateTime tokenValidity;
    /**
     * token值
     */
    private String tokenValue;

    /**
     * 账户状态
     */
    private String accountState;

    @Enumerated(EnumType.STRING)
    private MarketingChannel marketingChannel;

    @Enumerated(EnumType.STRING)
    private LoginChannel loginChannel;

    public MarketingChannel getMarketingChannel() {
        return marketingChannel;
    }

    public void setMarketingChannel(MarketingChannel marketingChannel) {
        this.marketingChannel = marketingChannel;
    }

    public String getAccountState() {
        return accountState;
    }

    public void setAccountState(String accountState) {
        this.accountState = accountState;
    }

    public String getOutUserId() {
        return outUserId;
    }

    public void setOutUserId(String outUserId) {
        this.outUserId = outUserId;
    }

    public LocalDateTime getTokenValidity() {
        return tokenValidity;
    }

    public void setTokenValidity(LocalDateTime tokenValidity) {
        this.tokenValidity = tokenValidity;
    }

    public String getTokenValue() {
        return tokenValue;
    }

    public void setTokenValue(String tokenValue) {
        this.tokenValue = tokenValue;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPwdSettingState() {
        return pwdSettingState;
    }

    public void setPwdSettingState(String pwdSettingState) {
        this.pwdSettingState = pwdSettingState;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public LoginChannel getLoginChannel() {
        return loginChannel;
    }

    public void setLoginChannel(LoginChannel loginChannel) {
        this.loginChannel = loginChannel;
    }

    @Override
    protected String prefix() {
        return "QH";
    }
}
