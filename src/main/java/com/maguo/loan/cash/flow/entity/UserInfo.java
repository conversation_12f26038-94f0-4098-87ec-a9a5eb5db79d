package com.maguo.loan.cash.flow.entity;


import com.maguo.loan.cash.flow.enums.Car;
import com.maguo.loan.cash.flow.enums.Education;
import com.maguo.loan.cash.flow.enums.House;
import com.maguo.loan.cash.flow.enums.Industry;
import com.maguo.loan.cash.flow.enums.Marriage;
import com.maguo.loan.cash.flow.enums.Position;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
@Entity
@Table(name = "user_info")
public class UserInfo extends BaseEntity {
    private String certNo;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 姓名
     */
    private String name;
    /**
     * 婚姻
     */
    @Enumerated(EnumType.STRING)
    private Marriage marriage;
    /**
     * 学历
     */
    @Enumerated(EnumType.STRING)
    private Education education;
    /**
     * A
     */
    private String acardScore;
    /**
     * B
     */
    private String bcardScore;
    /**
     * 月收入
     */
    private Integer income;
    /**
     * 行业
     */
    @Enumerated(EnumType.STRING)
    private Industry industry;
    /**
     * 职业
     */
    @Enumerated(EnumType.STRING)
    private Position position;

    @Enumerated(EnumType.STRING)
    private House houseInfo;

    @Enumerated(EnumType.STRING)
    private Car carInfo;
    /**
     * 电子邮箱
     */
    private String email;
    /**
     * 居住地
     */
    private String livingAddress;
    /**
     * 省
     */
    private String livingProvinceCode;
    /**
     * 市
     */
    private String livingCityCode;
    /**
     * 区
     */
    private String livingDistrictCode;
    /**
     * 街道
     */
    private String livingStreet;
    /**
     * 工作单位
     */
    private String unit;
    /**
     * 工作地址
     */
    private String unitAddress;
    /**
     * 省
     */
    private String unitProvinceCode;
    /**
     * 市
     */
    private String unitCityCode;
    /**
     * 区
     */
    private String unitDistrictCode;
    /**
     * 街道
     */
    private String unitStreet;

    private String unitPhone;

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }



    public String getAcardScore() {
        return acardScore;
    }

    public void setAcardScore(String acardScore) {
        this.acardScore = acardScore;
    }

    public String getBcardScore() {
        return bcardScore;
    }

    public void setBcardScore(String bcardScore) {
        this.bcardScore = bcardScore;
    }

    public Integer getIncome() {
        return income;
    }

    public void setIncome(Integer income) {
        this.income = income;
    }

    public House getHouseInfo() {
        return houseInfo;
    }

    public void setHouseInfo(House houseInfo) {
        this.houseInfo = houseInfo;
    }

    public Car getCarInfo() {
        return carInfo;
    }

    public void setCarInfo(Car carInfo) {
        this.carInfo = carInfo;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getLivingAddress() {
        return livingAddress;
    }

    public void setLivingAddress(String livingAddress) {
        this.livingAddress = livingAddress;
    }

    public String getLivingProvinceCode() {
        return livingProvinceCode;
    }

    public void setLivingProvinceCode(String livingProvinceCode) {
        this.livingProvinceCode = livingProvinceCode;
    }

    public String getLivingCityCode() {
        return livingCityCode;
    }

    public void setLivingCityCode(String livingCityCode) {
        this.livingCityCode = livingCityCode;
    }

    public String getLivingDistrictCode() {
        return livingDistrictCode;
    }

    public void setLivingDistrictCode(String livingDistrictCode) {
        this.livingDistrictCode = livingDistrictCode;
    }

    public String getLivingStreet() {
        return livingStreet;
    }

    public void setLivingStreet(String livingStreet) {
        this.livingStreet = livingStreet;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getUnitAddress() {
        return unitAddress;
    }

    public void setUnitAddress(String unitAddress) {
        this.unitAddress = unitAddress;
    }

    public String getUnitProvinceCode() {
        return unitProvinceCode;
    }

    public void setUnitProvinceCode(String unitProvinceCode) {
        this.unitProvinceCode = unitProvinceCode;
    }

    public String getUnitCityCode() {
        return unitCityCode;
    }

    public void setUnitCityCode(String unitCityCode) {
        this.unitCityCode = unitCityCode;
    }

    public String getUnitDistrictCode() {
        return unitDistrictCode;
    }

    public void setUnitDistrictCode(String unitDistrictCode) {
        this.unitDistrictCode = unitDistrictCode;
    }

    public String getUnitStreet() {
        return unitStreet;
    }

    public void setUnitStreet(String unitStreet) {
        this.unitStreet = unitStreet;
    }

    public Marriage getMarriage() {
        return marriage;
    }

    public void setMarriage(Marriage marriage) {
        this.marriage = marriage;
    }

    public Education getEducation() {
        return education;
    }

    public void setEducation(Education education) {
        this.education = education;
    }

    public Industry getIndustry() {
        return industry;
    }

    public void setIndustry(Industry industry) {
        this.industry = industry;
    }

    public Position getPosition() {
        return position;
    }

    public void setPosition(Position position) {
        this.position = position;
    }

    public String getUnitPhone() {
        return unitPhone;
    }

    public void setUnitPhone(String unitPhone) {
        this.unitPhone = unitPhone;
    }

    @Override
    protected String prefix() {
        return "UI";
    }
}
