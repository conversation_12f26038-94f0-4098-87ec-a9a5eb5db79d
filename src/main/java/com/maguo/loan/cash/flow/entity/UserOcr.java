package com.maguo.loan.cash.flow.entity;

import com.maguo.loan.cash.flow.enums.Gender;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
@Entity
@Table(name = "user_ocr")
public class UserOcr extends BaseEntity {

    private String userId;
    /**
     * 身份证号
     */
    private String certNo;
    /**
     * 地址
     */
    private String certAddress;
    /**
     * 签发机关
     */
    private String certSignOrg;
    /**
     * 有效期开始
     */
    private LocalDate certValidStart;
    /**
     * 有效期截止
     */
    private LocalDate certValidEnd;
    /**
     * 人头
     */
    private String headOssBucket;
    /**
     * 人头
     */
    private String headOssKey;
    /**
     * 国徽
     */
    private String nationOssBucket;
    /**
     * 国徽
     */
    private String nationOssKey;
    /**
     * 省
     */
    private String provinceCode;
    /**
     * 市
     */
    private String cityCode;
    /**
     * 区
     */
    private String districtCode;
    /**
     * 性别
     */
    @Enumerated(EnumType.STRING)
    private Gender gender;
    /**
     * 民族
     */
    private String nation;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getCertAddress() {
        return certAddress;
    }

    public void setCertAddress(String certAddress) {
        this.certAddress = certAddress;
    }

    public String getCertSignOrg() {
        return certSignOrg;
    }

    public void setCertSignOrg(String certSignOrg) {
        this.certSignOrg = certSignOrg;
    }

    public LocalDate getCertValidStart() {
        return certValidStart;
    }

    public void setCertValidStart(LocalDate certValidStart) {
        this.certValidStart = certValidStart;
    }

    public LocalDate getCertValidEnd() {
        return certValidEnd;
    }

    public void setCertValidEnd(LocalDate certValidEnd) {
        this.certValidEnd = certValidEnd;
    }

    public String getHeadOssBucket() {
        return headOssBucket;
    }

    public void setHeadOssBucket(String headOssBucket) {
        this.headOssBucket = headOssBucket;
    }

    public String getHeadOssKey() {
        return headOssKey;
    }

    public void setHeadOssKey(String headOssKey) {
        this.headOssKey = headOssKey;
    }

    public String getNationOssBucket() {
        return nationOssBucket;
    }

    public void setNationOssBucket(String nationOssBucket) {
        this.nationOssBucket = nationOssBucket;
    }

    public String getNationOssKey() {
        return nationOssKey;
    }

    public void setNationOssKey(String nationOssKey) {
        this.nationOssKey = nationOssKey;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getDistrictCode() {
        return districtCode;
    }

    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode;
    }

    public Gender getGender() {
        return gender;
    }

    public void setGender(Gender gender) {
        this.gender = gender;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    @Override
    protected String prefix() {
        return "OCR";
    }
}
