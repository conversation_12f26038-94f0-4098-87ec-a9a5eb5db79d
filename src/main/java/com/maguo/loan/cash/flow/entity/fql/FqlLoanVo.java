package com.maguo.loan.cash.flow.entity.fql;

import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.maguo.loan.cash.flow.enums.ProcessState;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;

import java.math.BigDecimal;

/**
 * 分期乐放款明细文件
 * @公司 中数金智(上海)有限公司
 */
public class FqlLoanVo {

    /**
     * 授信申请编号
     */
    private String outerLoanId;

    /**
     * 资方主键id
     */
    private String loanId;

    /**
     * 放款成功时间
     */
    private String loanTime;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 期数
     */
    private Integer periods;

    /**
     * 放款状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessState loanState;

    /**
     * 备用字段
     * 区分担保方/其他扩展使用
     */
    @Enumerated(EnumType.STRING)
    private GuaranteeCompany guaranteeCompany;

    public String getOuterLoanId() {
        return outerLoanId;
    }

    public void setOuterLoanId(String outerLoanId) {
        this.outerLoanId = outerLoanId;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getLoanTime() {
        return loanTime;
    }

    public void setLoanTime(String loanTime) {
        this.loanTime = loanTime;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getPeriods() {
        return periods;
    }

    public void setPeriods(Integer periods) {
        this.periods = periods;
    }

    public ProcessState getLoanState() {
        return loanState;
    }

    public void setLoanState(ProcessState loanState) {
        this.loanState = loanState;
    }

    public GuaranteeCompany getGuaranteeCompany() {
        return guaranteeCompany;
    }

    public void setGuaranteeCompany(GuaranteeCompany guaranteeCompany) {
        this.guaranteeCompany = guaranteeCompany;
    }

    @Override
    public String toString() {
        return "FqlLoanVo{" +
            "outerLoanId='" + outerLoanId + '\'' +
            ", loanId='" + loanId + '\'' +
            ", loanTime='" + loanTime + '\'' +
            ", amount=" + amount +
            ", periods=" + periods +
            ", loanState=" + loanState +
            ", guaranteeCompany=" + guaranteeCompany +
            '}';
    }
}
