package com.maguo.loan.cash.flow.entity.fql;

import java.math.BigDecimal;

/**
 * 还款明细文件
 * @公司 中数金智(上海)有限公司
 * @包名 com.maguo.loan.cash.flow.entity.vo.RepayDetailVo
 * @作者 Mr.sandman
 * @时间 2025/08/11 18:05
 */
public class FqlRepayDetailVo {

    /**
     * 资产号
     */
    private String outerRepayNo;
    /**
     * 借据号
     */
    private String loanId;
    /**
     * 结算日
     */
    private String repaidDate;
    /**
     * 当前期数
     */
    private Integer period;
    /**
     * 还款类型
     * 10-正常还款
     * 30-提前结清
     * 40-逾期还款
     * 50-代偿
     */
    private Integer repayType;
    /**
     * 还款金额
     */
    private BigDecimal totalAmt;
    /**
     * 还款本金
     */
    private BigDecimal principalAmt;
    /**
     * 还款利息
     */
    private BigDecimal interestAmt;
    /**
     * 还款罚息
     */
    private BigDecimal penaltyAmt;
    /**
     * 是否代偿
     * <p>
     * 取值说明：
     * Y：是
     * N：否
     */
    private String isDc;
    /**
     * 备用字段/扩展使用
     * 归还担保费
     */
    private BigDecimal guaranteeFeeAmt;

    public String getOuterRepayNo() {
        return outerRepayNo;
    }

    public void setOuterRepayNo(String outerRepayNo) {
        this.outerRepayNo = outerRepayNo;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getRepaidDate() {
        return repaidDate;
    }

    public void setRepaidDate(String repaidDate) {
        this.repaidDate = repaidDate;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public Integer getRepayType() {
        return repayType;
    }

    public void setRepayType(Integer repayType) {
        this.repayType = repayType;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public BigDecimal getPrincipalAmt() {
        return principalAmt;
    }

    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    public BigDecimal getInterestAmt() {
        return interestAmt;
    }

    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    public BigDecimal getPenaltyAmt() {
        return penaltyAmt;
    }

    public void setPenaltyAmt(BigDecimal penaltyAmt) {
        this.penaltyAmt = penaltyAmt;
    }

    public String getIsDc() {
        return isDc;
    }

    public void setIsDc(String isDc) {
        this.isDc = isDc;
    }

    public BigDecimal getGuaranteeFeeAmt() {
        return guaranteeFeeAmt;
    }

    public void setGuaranteeFeeAmt(BigDecimal guaranteeFeeAmt) {
        this.guaranteeFeeAmt = guaranteeFeeAmt;
    }

    @Override
    public String toString() {
        return "FqlRepayDetailVo{" +
            "outerRepayNo='" + outerRepayNo + '\'' +
            ", loanId='" + loanId + '\'' +
            ", repaidDate='" + repaidDate + '\'' +
            ", period='" + period + '\'' +
            ", repayType='" + repayType + '\'' +
            ", totalAmt=" + totalAmt +
            ", principalAmt=" + principalAmt +
            ", interestAmt=" + interestAmt +
            ", penaltyAmt=" + penaltyAmt +
            ", isDc='" + isDc + '\'' +
            ", guaranteeFeeAmt=" + guaranteeFeeAmt +
            '}';
    }
}
