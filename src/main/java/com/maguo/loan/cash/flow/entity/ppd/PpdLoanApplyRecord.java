package com.maguo.loan.cash.flow.entity.ppd;

import com.maguo.loan.cash.flow.entity.BaseEntity;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

import java.math.BigDecimal;


@Entity
@Table(name = "ppd_loan_apply_record")
public class PpdLoanApplyRecord extends BaseEntity {
    /**
     * 唯一请求流水号,后续用于放款查询,还款通知及影像传输
     */
    private String loanReqNo;

    /**
     * 合作方授信编号
     *
     * 2025-01-15 跟拍拍确认这个字段必传,的loanId
     */
    private String creditNo;

    /**
     * Y
     * N
     */
    private String revoke = "N";
    /**
     * 请求方代码
     */
    private String sourceCode;

    /**
     * 客户的真实姓名
     */
    private String custName;

    /**
     * 身份证号
     */
    private String idNo;

    /**
     * 手机号
     */
    private String mobileNo;

    /**
     * 借款金额，单位: 元
     */
    private BigDecimal loanAmt;

    /**
     * 期数
     */
    private Integer loanTerm;

    /**
     * 借款用途
     */
    private String loanPurpose;

    /**
     * 期限类型（暂时只支持按月）
     */
    private String periodType;

    /**
     * 还款方式（暂时只支持等额本息）
     */
    private String refundMethod;

    /**
     * 扣款日类型（暂时只支持放款日为扣款日）
     */
    private String chargeDateType;

    /**
     * 贷款类型（暂时只支持现金贷）
     */
    private String loanType;

    /**
     * 放款银行代码
     */
    private String bankCode;

    /**
     * 放款卡号
     */
    private String bankAcct;

    /**
     * 放款银行卡账户名
     */
    private String acctName;

    /**
     * 放款卡持卡人预留手机号
     */
    private String bankMobile;

    /**
     * 还款银行代码
     */
    private String repayBankCode;

    /**
     * 还款卡号
     */
    private String repayBankAcct;

    /**
     * 还款银行卡账户名
     */
    private String repayAcctName;

    /**
     * 还款卡持卡人预留手机号
     */
    private String repayBankMobile;

    /**
     * 签约协议渠道（如宝付支付）
     */
    private String bankChannel;

    /**
     * 签约协议号，只有与合作方签约共享时传入
     */
    private String channelRepayId;

    /**
     * 身份证有效期起始日期
     */
    private String idBeginDate;

    /**
     * 身份证有效期结束日期
     */
    private String idExpiryDate;

    /**
     * 民族，01汉族 02其他
     */
    private String nation;

    /**
     * 国籍，CHN-中国
     */
    private String nationality;

    /**
     * 联系人列表
     */
    private String contactList;

    public String getLoanReqNo() {
        return loanReqNo;
    }

    public void setLoanReqNo(String loanReqNo) {
        this.loanReqNo = loanReqNo;
    }

    public String getCreditNo() {
        return creditNo;
    }

    public void setCreditNo(String creditNo) {
        this.creditNo = creditNo;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getMobileNo() {
        return mobileNo;
    }

    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    public BigDecimal getLoanAmt() {
        return loanAmt;
    }

    public void setLoanAmt(BigDecimal loanAmt) {
        this.loanAmt = loanAmt;
    }

    public Integer getLoanTerm() {
        return loanTerm;
    }

    public void setLoanTerm(Integer loanTerm) {
        this.loanTerm = loanTerm;
    }

    public String getLoanPurpose() {
        return loanPurpose;
    }

    public void setLoanPurpose(String loanPurpose) {
        this.loanPurpose = loanPurpose;
    }

    public String getPeriodType() {
        return periodType;
    }

    public void setPeriodType(String periodType) {
        this.periodType = periodType;
    }

    public String getRefundMethod() {
        return refundMethod;
    }

    public void setRefundMethod(String refundMethod) {
        this.refundMethod = refundMethod;
    }

    public String getChargeDateType() {
        return chargeDateType;
    }

    public void setChargeDateType(String chargeDateType) {
        this.chargeDateType = chargeDateType;
    }

    public String getLoanType() {
        return loanType;
    }

    public void setLoanType(String loanType) {
        this.loanType = loanType;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankAcct() {
        return bankAcct;
    }

    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct;
    }

    public String getAcctName() {
        return acctName;
    }

    public void setAcctName(String acctName) {
        this.acctName = acctName;
    }

    public String getBankMobile() {
        return bankMobile;
    }

    public void setBankMobile(String bankMobile) {
        this.bankMobile = bankMobile;
    }

    public String getRepayBankCode() {
        return repayBankCode;
    }

    public void setRepayBankCode(String repayBankCode) {
        this.repayBankCode = repayBankCode;
    }

    public String getRepayBankAcct() {
        return repayBankAcct;
    }

    public void setRepayBankAcct(String repayBankAcct) {
        this.repayBankAcct = repayBankAcct;
    }

    public String getRepayAcctName() {
        return repayAcctName;
    }

    public void setRepayAcctName(String repayAcctName) {
        this.repayAcctName = repayAcctName;
    }

    public String getRepayBankMobile() {
        return repayBankMobile;
    }

    public void setRepayBankMobile(String repayBankMobile) {
        this.repayBankMobile = repayBankMobile;
    }

    public String getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(String bankChannel) {
        this.bankChannel = bankChannel;
    }

    public String getChannelRepayId() {
        return channelRepayId;
    }

    public void setChannelRepayId(String channelRepayId) {
        this.channelRepayId = channelRepayId;
    }

    public String getIdBeginDate() {
        return idBeginDate;
    }

    public void setIdBeginDate(String idBeginDate) {
        this.idBeginDate = idBeginDate;
    }

    public String getIdExpiryDate() {
        return idExpiryDate;
    }

    public void setIdExpiryDate(String idExpiryDate) {
        this.idExpiryDate = idExpiryDate;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getContactList() {
        return contactList;
    }

    public void setContactList(String contactList) {
        this.contactList = contactList;
    }

    public String getRevoke() {
        return revoke;
    }

    public void setRevoke(String revoke) {
        this.revoke = revoke;
    }
}
