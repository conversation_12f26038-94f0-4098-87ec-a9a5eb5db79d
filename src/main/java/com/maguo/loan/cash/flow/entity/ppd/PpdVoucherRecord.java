package com.maguo.loan.cash.flow.entity.ppd;

import com.maguo.loan.cash.flow.entity.BaseEntity;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "ppd_voucher_record")
public class PpdVoucherRecord extends BaseEntity {

    @Override
    protected String prefix() {
        return "VR";
    }
    /**
     * 放款id
     */
    private String loanId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件路径
     */
    private String filePath;

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
}
