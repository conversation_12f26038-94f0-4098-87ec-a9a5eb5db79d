package com.maguo.loan.cash.flow.entity.ppd;

import com.maguo.loan.cash.flow.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-08-11 16:24
 */
@Entity
@Table(name = "repay_benefit_allocate_record")
public class RepayBenefitAllocateRecord extends BaseEntity {
    /**
     * 关联的还款记录ID
     */
    @Column(name = "customer_repay_record_id", length = 32, nullable = false)
    private String customerRepayRecordId;

    /**
     * 借据ID
     */
    @Column(name = "loan_id", length = 32, nullable = false)
    private String loanId;

    /**
     * 期次
     */
    @Column(name = "period", nullable = false)
    private Integer period;

    /**
     * 外部还款流水号
     */
    @Column(name = "outer_repay_no", length = 50)
    private String outerRepayNo;

    /**
     * 还款成功时间
     */
    @Column(name = "repaid_date")
    private LocalDateTime repaidDate;

    /**
     * 获益方 CYBK 长银 HAOYUE 昊悦
     */
    @Column(name = "profit_party", length = 50, nullable = false)
    private String profitParty;

    /**
     * 非本金总金额(分润基数)
     */
    @Column(name = "total_non_principal", precision = 10, scale = 2, nullable = false)
    private BigDecimal totalNonPrincipal;

    /**
     * 分润方获取的总金额
     */
    @Column(name = "total_amount", precision = 10, scale = 2, nullable = false)
    private BigDecimal totalAmount;

    /**
     * 利息（银行）
     */
    @Column(name = "interest_amt", precision = 10, scale = 2)
    private BigDecimal interestAmt;

    /**
     * 罚息
     */
    @Column(name = "penalty_amt", precision = 10, scale = 2)
    private BigDecimal penaltyAmt;

    /**
     * 融担费
     */
    @Column(name = "guarantee_amt", precision = 10, scale = 2)
    private BigDecimal guaranteeAmt;

    /**
     * 咨询费
     */
    @Column(name = "consult_fee", precision = 10, scale = 2)
    private BigDecimal consultFee;

    /**
     * 额外的融担费
     */
    @Column(name = "extra_guarantee_amt", precision = 10, scale = 2)
    private BigDecimal extraGuaranteeAmt;

    /**
     * 违约金
     */
    @Column(name = "breach_amt", precision = 10, scale = 2)
    private BigDecimal breachAmt;

    /**
     * 剩余金额(总非本金-长银-昊悦)
     */
    @Column(name = "remaining_amount", precision = 10, scale = 2, nullable = false)
    private BigDecimal remainingAmount;

    /**
     * 审计状态:PASS,REJECT
     */
    @Column(name = "audit_status", length = 20)
    private String auditStatus;

    /**
     * 还款成功时间
     */
    @Column(name = "start_repay_time")
    private LocalDateTime startRepayTime;

    public LocalDateTime getStartRepayTime() {
        return startRepayTime;
    }

    public void setStartRepayTime(LocalDateTime startRepayTime) {
        this.startRepayTime = startRepayTime;
    }

    public String getCustomerRepayRecordId() {
        return customerRepayRecordId;
    }

    public void setCustomerRepayRecordId(String customerRepayRecordId) {
        this.customerRepayRecordId = customerRepayRecordId;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getOuterRepayNo() {
        return outerRepayNo;
    }

    public void setOuterRepayNo(String outerRepayNo) {
        this.outerRepayNo = outerRepayNo;
    }

    public LocalDateTime getRepaidDate() {
        return repaidDate;
    }

    public void setRepaidDate(LocalDateTime repaidDate) {
        this.repaidDate = repaidDate;
    }

    public String getProfitParty() {
        return profitParty;
    }

    public void setProfitParty(String profitParty) {
        this.profitParty = profitParty;
    }

    public BigDecimal getTotalNonPrincipal() {
        return totalNonPrincipal;
    }

    public void setTotalNonPrincipal(BigDecimal totalNonPrincipal) {
        this.totalNonPrincipal = totalNonPrincipal;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getInterestAmt() {
        return interestAmt;
    }

    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    public BigDecimal getPenaltyAmt() {
        return penaltyAmt;
    }

    public void setPenaltyAmt(BigDecimal penaltyAmt) {
        this.penaltyAmt = penaltyAmt;
    }

    public BigDecimal getGuaranteeAmt() {
        return guaranteeAmt;
    }

    public void setGuaranteeAmt(BigDecimal guaranteeAmt) {
        this.guaranteeAmt = guaranteeAmt;
    }

    public BigDecimal getConsultFee() {
        return consultFee;
    }

    public void setConsultFee(BigDecimal consultFee) {
        this.consultFee = consultFee;
    }

    public BigDecimal getExtraGuaranteeAmt() {
        return extraGuaranteeAmt;
    }

    public void setExtraGuaranteeAmt(BigDecimal extraGuaranteeAmt) {
        this.extraGuaranteeAmt = extraGuaranteeAmt;
    }

    public BigDecimal getBreachAmt() {
        return breachAmt;
    }

    public void setBreachAmt(BigDecimal breachAmt) {
        this.breachAmt = breachAmt;
    }

    public BigDecimal getRemainingAmount() {
        return remainingAmount;
    }

    public void setRemainingAmount(BigDecimal remainingAmount) {
        this.remainingAmount = remainingAmount;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }
}

