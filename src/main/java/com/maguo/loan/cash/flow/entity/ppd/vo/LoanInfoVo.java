package com.maguo.loan.cash.flow.entity.ppd.vo;

import java.math.BigDecimal;

/**
 * 拍拍放款对账文件详情
 * @公司 中数金智(上海)有限公司
 * @作者 Mr.sandman
 * @时间 2025/06/23 09:57
 */
public class LoanInfoVo {

    /**
     * 信也放款请求流水号
     */
    private String loanReqNo;

    /**
     * 请求方代码
     */
    private String sourceCode;

    /**
     * 资金到账日期(格式：YYYY-MM-DD)
     */
    private String cashDate;

    /**
     * 放款成功金额(单位：元)
     */
    private BigDecimal loanAmt;

    /**
     * 获取信也放款请求流水号
     * @return 信也放款请求流水号
     */
    public String getLoanReqNo() {
        return loanReqNo;
    }

    /**
     * 设置信也放款请求流水号
     * @param loanReqNo 信也放款请求流水号
     */
    public void setLoanReqNo(String loanReqNo) {
        this.loanReqNo = loanReqNo;
    }

    /**
     * 获取请求方代码
     * @return 请求方代码
     */
    public String getSourceCode() {
        return sourceCode;
    }

    /**
     * 设置请求方代码
     * @param sourceCode 请求方代码
     */
    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    /**
     * 获取资金到账日期
     * @return 资金到账日期(格式：YYYY-MM-DD)
     */
    public String getCashDate() {
        return cashDate;
    }

    /**
     * 设置资金到账日期
     * @param cashDate 资金到账日期(格式：YYYY-MM-DD)
     */
    public void setCashDate(String cashDate) {
        this.cashDate = cashDate;
    }

    /**
     * 获取放款成功金额
     * @return 放款成功金额(单位：元)
     */
    public BigDecimal getLoanAmt() {
        return loanAmt;
    }

    /**
     * 设置放款成功金额
     * @param loanAmt 放款成功金额(单位：元)
     */
    public void setLoanAmt( BigDecimal loanAmt ) {
        this.loanAmt = loanAmt;
    }
}
