package com.maguo.loan.cash.flow.entity.ppd.vo;

import java.math.BigDecimal;

/**
 * 还款信息实体类
 * @公司 中数金智(上海)有限公司
 * @作者 Mr.sandman
 * @时间 2025/06/23 10:04
 */
public class RepaymentInfoVo {

    /**
     * 放款请求流水号（唯一放款请求流水号）
     */
    private String loanReqNo;

    /**
     * 请求方代码（渠道来源或产品号）
     */
    private String sourceCode;

    /**
     * 还款请求流水号（唯一还款请求流水号）
     */
    private String repayNo;

    /**
     * 还款模式
     * 01:线上还款
     * 02:线下还款
     */
    private String repayMode;

    /**
     * 还款类型
     * 01:按期正常还款
     * 02:按期提前还款
     * 03:一次性提前结清
     * 04:逾期整期还款
     * 05:逾期部分还款（最后一部分传04）
     */
    private String repayType;

    /**
     * 还款日期（格式：yyyyMMdd）
     */
    private String repayDate;

    /**
     * 还款期次（结清还款信息始期次）
     */
    private Integer repayTerm;

    /**
     * 还款总金额（单位：元）
     */
    private BigDecimal repayAmount;

    /**
     * 还款本金（单位：元）
     */
    private BigDecimal repayPrincipal;

    /**
     * 还款利息（单位：元）
     */
    private BigDecimal repayInterest;

    /**
     * 还款罚息（单位：元）
     */
    private BigDecimal repayOverdue;

    /**
     * 还款担保费（单位：元）
     */
    private BigDecimal repayPoundage;

    /**
     * 还款提结违约金（单位：元）
     */
    private BigDecimal repayLateFee;

    public String getLoanReqNo() {
        return loanReqNo;
    }

    public void setLoanReqNo( String loanReqNo ) {
        this.loanReqNo = loanReqNo;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode( String sourceCode ) {
        this.sourceCode = sourceCode;
    }

    public String getRepayNo() {
        return repayNo;
    }

    public void setRepayNo( String repayNo ) {
        this.repayNo = repayNo;
    }

    public String getRepayMode() {
        return repayMode;
    }

    public void setRepayMode( String repayMode ) {
        this.repayMode = repayMode;
    }

    public String getRepayType() {
        return repayType;
    }

    public void setRepayType( String repayType ) {
        this.repayType = repayType;
    }

    public String getRepayDate() {
        return repayDate;
    }

    public void setRepayDate( String repayDate ) {
        this.repayDate = repayDate;
    }

    public Integer getRepayTerm() {
        return repayTerm;
    }

    public void setRepayTerm( Integer repayTerm ) {
        this.repayTerm = repayTerm;
    }

    public BigDecimal getRepayAmount() {
        return repayAmount;
    }

    public void setRepayAmount( BigDecimal repayAmount ) {
        this.repayAmount = repayAmount;
    }

    public BigDecimal getRepayPrincipal() {
        return repayPrincipal;
    }

    public void setRepayPrincipal( BigDecimal repayPrincipal ) {
        this.repayPrincipal = repayPrincipal;
    }

    public BigDecimal getRepayInterest() {
        return repayInterest;
    }

    public void setRepayInterest( BigDecimal repayInterest ) {
        this.repayInterest = repayInterest;
    }

    public BigDecimal getRepayOverdue() {
        return repayOverdue;
    }

    public void setRepayOverdue( BigDecimal repayOverdue ) {
        this.repayOverdue = repayOverdue;
    }

    public BigDecimal getRepayPoundage() {
        return repayPoundage;
    }

    public void setRepayPoundage( BigDecimal repayPoundage ) {
        this.repayPoundage = repayPoundage;
    }

    public BigDecimal getRepayLateFee() {
        return repayLateFee;
    }

    public void setRepayLateFee( BigDecimal repayLateFee ) {
        this.repayLateFee = repayLateFee;
    }
}
