package com.maguo.loan.cash.flow.entity.vo;

import java.math.BigDecimal;

/**
 * 借据明细文件
 * @公司 中数金智(上海)有限公司
 * @包名 com.maguo.loan.cash.flow.entity.vo.loanVo
 * @作者 Mr.sandman
 * @时间 2025/05/28 16:01
 */
public class LoanVo {

    /**
     * 对方业务号
     */
    private String outAppSeq;

    /**
     * 客户编号
     */
    private String custId;

    /**
     * 用款申请日期(yyyy-MM-dd)
     */
    private String applyDt;

    /**
     * 用款申请流水号
     */
    private String applSeq;

    /**
     * 合同号
     */
    private String contNo;

    /**
     * 借据号
     */
    private String loanNo;

    /**
     * 放款金额
     */
    private BigDecimal dnAmt;

    /**
     * 申请期限(单位由业务决定)
     */
    private Integer applyTnr;

    /**
     * 年化利率
     */
    private BigDecimal basicIntRat;

    /**
     * 放款时间(yyyy-MM-dd)
     */
    private String loanActvDt;

    /**
     * 合同到期日(yyyy-MM-dd)
     */
    private String contEndDt;

    /**
     * 放款账户姓名
     */
    private String acctName;

    /**
     * 放款账户开户行
     */
    private String acctBank;

    /**
     * 放款账号
     */
    private String acctNo;

    // 以下是getter和setter方法
    public String getOutAppSeq() {
        return outAppSeq;
    }

    public void setOutAppSeq(String outAppSeq) {
        this.outAppSeq = outAppSeq;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getApplyDt() {
        return applyDt;
    }

    public void setApplyDt(String applyDt) {
        this.applyDt = applyDt;
    }

    public String getApplSeq() {
        return applSeq;
    }

    public void setApplSeq(String applSeq) {
        this.applSeq = applSeq;
    }

    public String getContNo() {
        return contNo;
    }

    public void setContNo(String contNo) {
        this.contNo = contNo;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public BigDecimal getDnAmt() {
        return dnAmt;
    }

    public void setDnAmt(BigDecimal dnAmt) {
        this.dnAmt = dnAmt;
    }

    public Integer getApplyTnr() {
        return applyTnr;
    }

    public void setApplyTnr(Integer applyTnr) {
        this.applyTnr = applyTnr;
    }

    public BigDecimal getBasicIntRat() {
        return basicIntRat;
    }

    public void setBasicIntRat(BigDecimal basicIntRat) {
        this.basicIntRat = basicIntRat;
    }

    public String getLoanActvDt() {
        return loanActvDt;
    }

    public void setLoanActvDt(String loanActvDt) {
        this.loanActvDt = loanActvDt;
    }

    public String getContEndDt() {
        return contEndDt;
    }

    public void setContEndDt(String contEndDt) {
        this.contEndDt = contEndDt;
    }

    public String getAcctName() {
        return acctName;
    }

    public void setAcctName(String acctName) {
        this.acctName = acctName;
    }

    public String getAcctBank() {
        return acctBank;
    }

    public void setAcctBank(String acctBank) {
        this.acctBank = acctBank;
    }

    public String getAcctNo() {
        return acctNo;
    }

    public void setAcctNo(String acctNo) {
        this.acctNo = acctNo;
    }

    @Override
    public String toString() {
        return "LoanBusinessInfo{" +
            "outAppSeq='" + outAppSeq + '\'' +
            ", custId='" + custId + '\'' +
            ", applyDt='" + applyDt + '\'' +
            ", applSeq='" + applSeq + '\'' +
            ", contNo='" + contNo + '\'' +
            ", loanNo='" + loanNo + '\'' +
            ", dnAmt=" + dnAmt +
            ", applyTnr=" + applyTnr +
            ", basicIntRat=" + basicIntRat +
            ", loanActvDt='" + loanActvDt + '\'' +
            ", contEndDt='" + contEndDt + '\'' +
            ", acctName='" + acctName + '\'' +
            ", acctBank='" + acctBank + '\'' +
            ", acctNo='" + acctNo + '\'' +
            '}';
    }

}
