package com.maguo.loan.cash.flow.entity.vo;

import java.math.BigDecimal;

/**
 * 还款后还款计划文件
 * @公司 中数金智(上海)有限公司
 * @包名 com.maguo.loan.cash.flow.entity.vo.RepayAfterVo
 * @作者 Mr.sandman
 * @时间 2025/05/28 16:06
 */
public class RepayAfterVo {

    /**
     * 对方业务号
     */
    private String outApplSeq;
    /**
     * 借据号
     */
    private String loanNo;
    /**
     * 期次号
     */
    private BigDecimal tnr;
    /**
     * 到期日，日期格式：yyyy-MM-dd
     */
    private String dueDt;
    /**
     * 应还总金额
     */
    private BigDecimal shTotalAmt;
    /**
     * 应还本金
     */
    private BigDecimal shPrcpAmt;
    /**
     * 应还利息
     */
    private BigDecimal shIntAmt;
    /**
     * 应还罚息
     */
    private BigDecimal shOdIntAmt;
    /**
     * 应还复利金额
     */
    private BigDecimal shCommIntAmt;
    /**
     * 应还费用金额
     */
    private BigDecimal shFeeAmt;
    /**
     * 应还担保费金额
     */
    private BigDecimal shGuarAmt;
    /**
     * 应还担保费逾期费用
     */
    private BigDecimal shGuarOdAmt;
    /**
     * 实还总金额
     */
    private BigDecimal acTotalAmt;
    /**
     * 实还本金
     */
    private BigDecimal acPrcpAmt;
    /**
     * 实还利息
     */
    private BigDecimal acIntAmt;
    /**
     * 实还罚息
     */
    private BigDecimal acOdIntAmt;
    /**
     * 实还复利金额
     */
    private BigDecimal acCommIntAmt;
    /**
     * 实还费用金额
     */
    private BigDecimal acFeeAmt;
    /**
     * 实还担保费金额
     */
    private BigDecimal acGuarAmt;
    /**
     * 实还担保费罚息金额
     */
    private BigDecimal acGuarOdAmt;
    /**
     * 是否结清（Y-是，N-否）
     */
    private String isSetl;
    /**
     * 结清日期，日期格式：yyyy-MM-dd
     */
    private String setlDt;

    public String getOutApplSeq() {
        return outApplSeq;
    }

    public void setOutApplSeq( String outApplSeq ) {
        this.outApplSeq = outApplSeq;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo( String loanNo ) {
        this.loanNo = loanNo;
    }

    public BigDecimal getTnr() {
        return tnr;
    }

    public void setTnr( BigDecimal tnr ) {
        this.tnr = tnr;
    }

    public String getDueDt() {
        return dueDt;
    }

    public void setDueDt( String dueDt ) {
        this.dueDt = dueDt;
    }

    public BigDecimal getShTotalAmt() {
        return shTotalAmt;
    }

    public void setShTotalAmt( BigDecimal shTotalAmt ) {
        this.shTotalAmt = shTotalAmt;
    }

    public BigDecimal getShPrcpAmt() {
        return shPrcpAmt;
    }

    public void setShPrcpAmt( BigDecimal shPrcpAmt ) {
        this.shPrcpAmt = shPrcpAmt;
    }

    public BigDecimal getShIntAmt() {
        return shIntAmt;
    }

    public void setShIntAmt( BigDecimal shIntAmt ) {
        this.shIntAmt = shIntAmt;
    }

    public BigDecimal getShOdIntAmt() {
        return shOdIntAmt;
    }

    public void setShOdIntAmt( BigDecimal shOdIntAmt ) {
        this.shOdIntAmt = shOdIntAmt;
    }

    public BigDecimal getShCommIntAmt() {
        return shCommIntAmt;
    }

    public void setShCommIntAmt( BigDecimal shCommIntAmt ) {
        this.shCommIntAmt = shCommIntAmt;
    }

    public BigDecimal getShFeeAmt() {
        return shFeeAmt;
    }

    public void setShFeeAmt( BigDecimal shFeeAmt ) {
        this.shFeeAmt = shFeeAmt;
    }

    public BigDecimal getShGuarAmt() {
        return shGuarAmt;
    }

    public void setShGuarAmt( BigDecimal shGuarAmt ) {
        this.shGuarAmt = shGuarAmt;
    }

    public BigDecimal getShGuarOdAmt() {
        return shGuarOdAmt;
    }

    public void setShGuarOdAmt( BigDecimal shGuarOdAmt ) {
        this.shGuarOdAmt = shGuarOdAmt;
    }

    public BigDecimal getAcTotalAmt() {
        return acTotalAmt;
    }

    public void setAcTotalAmt( BigDecimal acTotalAmt ) {
        this.acTotalAmt = acTotalAmt;
    }

    public BigDecimal getAcPrcpAmt() {
        return acPrcpAmt;
    }

    public void setAcPrcpAmt( BigDecimal acPrcpAmt ) {
        this.acPrcpAmt = acPrcpAmt;
    }

    public BigDecimal getAcIntAmt() {
        return acIntAmt;
    }

    public void setAcIntAmt( BigDecimal acIntAmt ) {
        this.acIntAmt = acIntAmt;
    }

    public BigDecimal getAcOdIntAmt() {
        return acOdIntAmt;
    }

    public void setAcOdIntAmt( BigDecimal acOdIntAmt ) {
        this.acOdIntAmt = acOdIntAmt;
    }

    public BigDecimal getAcCommIntAmt() {
        return acCommIntAmt;
    }

    public void setAcCommIntAmt( BigDecimal acCommIntAmt ) {
        this.acCommIntAmt = acCommIntAmt;
    }

    public BigDecimal getAcFeeAmt() {
        return acFeeAmt;
    }

    public void setAcFeeAmt( BigDecimal acFeeAmt ) {
        this.acFeeAmt = acFeeAmt;
    }

    public BigDecimal getAcGuarAmt() {
        return acGuarAmt;
    }

    public void setAcGuarAmt( BigDecimal acGuarAmt ) {
        this.acGuarAmt = acGuarAmt;
    }

    public BigDecimal getAcGuarOdAmt() {
        return acGuarOdAmt;
    }

    public void setAcGuarOdAmt( BigDecimal acGuarOdAmt ) {
        this.acGuarOdAmt = acGuarOdAmt;
    }

    public String getIsSetl() {
        return isSetl;
    }

    public void setIsSetl( String isSetl ) {
        this.isSetl = isSetl;
    }

    public String getSetlDt() {
        return setlDt;
    }

    public void setSetlDt( String setlDt ) {
        this.setlDt = setlDt;
    }


    @Override
    public String toString() {
        return "RepayAfterVo{" + "outApplSeq='" + outApplSeq + '\'' + ", loanNo='" + loanNo + '\'' + ", tnr=" + tnr + ", dueDt='" + dueDt + '\'' + ", shTotalAmt=" + shTotalAmt + ", shPrcpAmt=" + shPrcpAmt + ", shIntAmt=" + shIntAmt + ", shOdIntAmt=" + shOdIntAmt + ", shCommIntAmt=" + shCommIntAmt + ", shFeeAmt=" + shFeeAmt + ", shGuarAmt=" + shGuarAmt + ", shGuarOdAmt=" + shGuarOdAmt + ", acTotalAmt=" + acTotalAmt + ", acPrcpAmt=" + acPrcpAmt + ", acIntAmt=" + acIntAmt + ", acOdIntAmt=" + acOdIntAmt + ", acCommIntAmt=" + acCommIntAmt + ", acFeeAmt=" + acFeeAmt + ", acGuarAmt=" + acGuarAmt + ", acGuarOdAmt=" + acGuarOdAmt + ", isSetl='" + isSetl + '\'' + ", setlDt='" + setlDt + '\'' + '}';
    }
}
