package com.maguo.loan.cash.flow.enums;

/**
 * <AUTHOR>
 * @since 2025-02-20
 */
public enum AppCard {

    SINGLE_APPLY(AppCard.builder()
        .title("最高可借额度（元）")
        .buttonText("申请借钱")
        .buttonFloatText("即刻获取额度")
        .amountType(AmountType.SINGLE)
        .build()),
    SINGLE_AUDIT_REJECT(AppCard.builder()
        .rejectTitle("很抱歉！审核未通过")
        .rejectSubtitle("请于申请日30天后重新尝试借款")
        .amountType(AmountType.SINGLE)
        .build()),
    SINGLE_AUDITING(AppCard.builder()
        .title("最高可借额度（元）")
        .buttonText("审核中")
        .buttonFloatText("预计1分钟")
        .amountType(AmountType.SINGLE)
        .build()),
    SINGLE_WITHDRAW(AppCard.builder()
        .title("可用额度（元）")
        .subTitle("总额度（元）%s")
        .buttonText("去提现")
        .buttonFloatText("快速到账")
        .amountType(AmountType.SINGLE)
        .build()),
    SINGLE_LOANING(AppCard.builder()
        .title("已用额度（元）")
        .subTitle("总额度（元）%s")
        .buttonText("放款中")
        .buttonFloatText("快速到账")
        .amountType(AmountType.SINGLE)
        .build()),
    SINGLE_NOT_BUY_RIGHTS(AppCard.builder()
        .title("已用额度（元）")
        .subTitle("总额度（元）%s")
        .buttonText("审核中")
        .buttonFloatText("快速到账")
        .amountType(AmountType.SINGLE)
        .build()),
    SINGLE_NOT_BUY_RIGHTS_LOAN_CANCEL(AppCard.builder()
        .title("很抱歉！资金匹配失败")
        .subTitle("请重新尝试借款")
        .buttonText("申请借钱")
        .buttonFloatText("快速到账")
        .amountType(AmountType.SINGLE)
        .build()),
    SINGLE_APPLY_AGAIN(AppCard.builder()
        .title("预估可借额度（元）")
        .buttonText("再借一笔")
        .buttonFloatText("缓解资金压力")
        .bottomText("还款日期 %s")
        .overdueText("您已逾期%s天")
        .amountType(AmountType.SINGLE)
        .build()),
    SINGLE_CLEAR(AppCard.builder()
        .title("最高可借额度（元）")
        .buttonText("再借一笔")
        .buttonFloatText("即刻获取额度")
        .amountType(AmountType.SINGLE)
        .build()),
    SINGLE_TO_REPAY(AppCard.builder()
        .title("%s待还（元）")
        .subTitle("可用额度（元）0.00")
        .buttonText("去还款")
        .buttonFloatText("有机会再借一笔")
        .amountType(AmountType.SINGLE)
        .build()),
    SINGLE_OVERDUE_TO_REPAY(AppCard.builder()
        .title("当前应还（元）")
        .subTitle("珍爱征信逾期将影响信用")
        .buttonText("立即还款")
        .buttonFloatText("珍爱征信")
        .bottomText("已产生罚息，未结清将影响信用")
        .overdueText("您已逾期%s天")
        .amountType(AmountType.SINGLE)
        .build()),
    /* ===  revolving card === */
    REVOLVING_AUDIT_REJECT(AppCard.builder()
        .rejectTitle("很抱歉！审核未通过")
        .rejectSubtitle("请于申请日30天后重新尝试借款")
        .amountType(AmountType.REVOLVING)
        .build()), // 无在贷时，二次风控被拒的
    REVOLVING_WITHDRAW(AppCard.builder()
        .title("可用额度（元）")
        .subTitle("总额度（元）%s")
        .buttonText("去提现")
        .buttonFloatText("快速到账")
        .readButtonText("查看")
        .amountType(AmountType.REVOLVING)
        .build()),
    REVOLVING_LOANING(AppCard.builder()
        .title("可用额度（元）")
        .subTitle("总额度（元）%s")
        .buttonText("放款中")
        .buttonFloatText("快速到账")
        .readButtonText("查看")
        .amountType(AmountType.REVOLVING)
        .build()),
    REVOLVING_NOT_BUY_RIGHTS(AppCard.builder()
        .title("可用额度（元）")
        .subTitle("总额度（元）%s")
        .buttonText("审核中")
        .buttonFloatText("快速到账")
        .readButtonText("查看")
        .amountType(AmountType.REVOLVING)
        .build()),
    REVOLVING_NOT_BUY_RIGHTS_LOAN_CANCEL(AppCard.builder()
        .title("很抱歉！资金匹配失败")
        .subTitle("请重新尝试借款")
        .buttonText("申请借钱")
        .buttonFloatText("快速到账")
        .amountType(AmountType.REVOLVING)
        .build()),
    REVOLVING_ALL_CLEAR(AppCard.builder()
        .title("可用额度（元）")
        .subTitle("总额度（元）%s")
        .buttonText("再借一笔")
        .buttonFloatText("快速到账")
        .readButtonText("查看")
        .amountType(AmountType.REVOLVING)
        .build()),
    REVOLVING_HAS_LOAN(AppCard.builder()
        .title("可用额度（元）")
        .subTitle("总额度（元）%s")
        .buttonText("再借一笔")
        .buttonFloatText("缓解资金压力")
        .bottomText("还款日期 %s")
        .readButtonText("查看")
        .amountType(AmountType.REVOLVING)
        .build()),
    REVOLVING_TO_REPAY(AppCard.builder()
        .title("%s待还（元）")
        .subTitle("可用额度（元）%s")
        .buttonText("去还款")
        .buttonFloatText("恢复额度")
        .readButtonText("查看")
        .amountType(AmountType.REVOLVING)
        .build()),
    REVOLVING_OVERDUE_TO_REPAY(AppCard.builder()
        .title("当前应还（元）")
        .subTitle("额度已冻结，珍爱征信逾期将影响信用")
        .buttonText("立即还款")
        .buttonFloatText("恢复额度")
        .bottomText("已产生罚息，未结清将影响信用")
        .overdueText("已逾期%s天")
        .amountType(AmountType.REVOLVING)
        .build());

    /**
     * 标题
     */
    private final String title;

    /**
     * 副标题
     */
    private final String subTitle;

    /**
     * 按钮文字
     */
    private final String buttonText;

    /**
     * 按钮悬浮文字
     */
    private final String buttonFloatText;

    /**
     * 阅读按钮文字
     */
    private final String readButtonText;

    /**
     * 底部文字
     */
    private final String bottomText;

    /**
     * 拒绝标题
     */
    private final String rejectTitle;

    /**
     * 拒绝副标题
     */
    private final String rejectSubtitle;

    /**
     * 拒绝按钮文字
     */
    private final String rejectButtonText;

    /**
     * 拒绝按钮悬浮文字
     */
    private final String rejectButtonFloatText;

    /**
     * 逾期文字
     */
    private final String overdueText;

    /**
     * 额度类型
     */
    private final AmountType amountType;

    AppCard(CardBuilder builder) {
        this.title = builder.title;
        this.subTitle = builder.subTitle;
        this.buttonText = builder.buttonText;
        this.buttonFloatText = builder.buttonFloatText;
        this.readButtonText = builder.readButtonText;
        this.bottomText = builder.bottomText;
        this.rejectTitle = builder.rejectTitle;
        this.rejectSubtitle = builder.rejectSubtitle;
        this.rejectButtonText = builder.rejectButtonText;
        this.rejectButtonFloatText = builder.rejectButtonFloatText;
        this.overdueText = builder.overdueText;
        this.amountType = builder.amountType;
    }

    public String getTitle() {
        return title;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public String getButtonText() {
        return buttonText;
    }

    public String getButtonFloatText() {
        return buttonFloatText;
    }

    public String getReadButtonText() {
        return readButtonText;
    }

    public String getBottomText() {
        return bottomText;
    }

    public String getRejectTitle() {
        return rejectTitle;
    }

    public String getRejectSubtitle() {
        return rejectSubtitle;
    }

    public String getRejectButtonText() {
        return rejectButtonText;
    }

    public String getRejectButtonFloatText() {
        return rejectButtonFloatText;
    }

    public String getOverdueText() {
        return overdueText;
    }

    public AmountType getAmountType() {
        return amountType;
    }

    public static class CardBuilder {
        private String title;
        private String subTitle;
        private String buttonText;
        private String buttonFloatText;
        private String readButtonText;
        private String bottomText;
        private String rejectTitle;
        private String rejectSubtitle;
        private String rejectButtonText;
        private String rejectButtonFloatText;
        private String overdueText;
        private AmountType amountType;

        public CardBuilder title(String title) {
            this.title = title;
            return this;
        }

        public CardBuilder subTitle(String subTitle) {
            this.subTitle = subTitle;
            return this;
        }

        public CardBuilder buttonText(String buttonText) {
            this.buttonText = buttonText;
            return this;
        }

        public CardBuilder buttonFloatText(String buttonFloatText) {
            this.buttonFloatText = buttonFloatText;
            return this;
        }

        public CardBuilder readButtonText(String readButtonText) {
            this.readButtonText = readButtonText;
            return this;
        }

        public CardBuilder bottomText(String bottomText) {
            this.bottomText = bottomText;
            return this;
        }

        public CardBuilder rejectTitle(String rejectTitle) {
            this.rejectTitle = rejectTitle;
            return this;
        }

        public CardBuilder rejectSubtitle(String rejectSubtitle) {
            this.rejectSubtitle = rejectSubtitle;
            return this;
        }

        public CardBuilder rejectButtonText(String rejectButtonText) {
            this.rejectButtonText = rejectButtonText;
            return this;
        }

        public CardBuilder rejectButtonFloatText(String rejectButtonFloatText) {
            this.rejectButtonFloatText = rejectButtonFloatText;
            return this;
        }

        public CardBuilder overdueText(String overdueText) {
            this.overdueText = overdueText;
            return this;
        }

        public CardBuilder amountType(AmountType amountType) {
            this.amountType = amountType;
            return this;
        }

        public CardBuilder build() {
            return this;
        }
    }

    public static CardBuilder builder() {
        return new CardBuilder();
    }
}
