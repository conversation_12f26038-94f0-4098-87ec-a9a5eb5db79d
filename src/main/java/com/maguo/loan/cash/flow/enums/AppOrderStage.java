package com.maguo.loan.cash.flow.enums;

/**
 * 借据阶段
 */
public enum AppOrderStage {
    RISK_EMPTY("200010", "借款未申请"),
    RISK_REJECT("200011", "风控审核拒绝"),
    RISK_AUDING("200012", "风控审核中"),
    RISK_PASS("200013", "风控通过，未授信"),
    CREDIT_PROCESSING("300010", "授信处理中"),
    CREDIT_FAILED("300011", "授信失败"),

    CREDIT_PASS("300012", "审核通过，待要款"),

    MONEY_MATCHING("500001", "资金匹配中"),
    LOAN_INIT("400009", "提现中"),
    LOAN_PROCESSING("400010", "要款中"),
    LOAN_FAILED("400011", "放款失败"),
    LOAN_SUCCESS("400012", "放款成功"),
    LOAN_CLEAR("400013", "放款已结清"),
    LOAN_CANCEL("400014", "放款取消");

    AppOrderStage(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getCode() {
        return code;
    }

    private final String desc;

    private final String code;

}
