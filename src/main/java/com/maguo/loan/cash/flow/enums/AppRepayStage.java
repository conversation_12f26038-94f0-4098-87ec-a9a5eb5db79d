package com.maguo.loan.cash.flow.enums;

/**
 * 还款阶段
 */
public enum AppRepayStage {
    NOT_LOAN("600010", "未有借款"),

    NORMAL("600011", "未还"),

    REPAID("600012", "已还"),

    OVERDUE("600013", "逾期");


    AppRepayStage(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getCode() {
        return code;
    }

    private final String desc;

    private final String code;

}
