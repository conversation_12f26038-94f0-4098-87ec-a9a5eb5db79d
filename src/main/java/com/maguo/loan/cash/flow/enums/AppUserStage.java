package com.maguo.loan.cash.flow.enums;

/**
 * 用户资料阶段
 */
public enum AppUserStage {
    USER_EMPTY("100010", "空白用户"),
    USER_ID_PASS("100011", "已经实名认证（ocr身份证）"),
    USER_FACE_PASS("100012", "已经人脸识别"),
    USER_CARD_PASS("100013", "已经绑定银行卡"),
    USER_INFO_PASS("100014", "已经填写个人信息"),
    USER_CONTACT_PASS("100015", "已经填写联系人");

    AppUserStage(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private final String desc;

    private final String code;

    public String getDesc() {
        return desc;
    }

    public String getCode() {
        return code;
    }
}
