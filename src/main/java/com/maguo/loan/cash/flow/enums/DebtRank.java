package com.maguo.loan.cash.flow.enums;

public enum DebtRank {

    ONE(1, "无负债"),
    TWO(2, "0-5万"),
    FIVE(5, "5万-30万"),
    NINE(9, "30万-50万"),
    SIXTEEN(16, "50万-100万"),
    TWENTY_TWO(22, "100万以上");
    private Integer code;
    private String desc;
    DebtRank(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
