package com.maguo.loan.cash.flow.enums;

/**
 * <AUTHOR>
 * @date 2024/6/12
 */
public enum Frequency {

    SUCCESS("000", "正常"),
    FREQUENCY_NUM("001", "更新身份证次数超过限制,明日再试"),
    EXISTS_ORDER("002", "存在在途订单");
    private String code;

    private String description;

    Frequency(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
