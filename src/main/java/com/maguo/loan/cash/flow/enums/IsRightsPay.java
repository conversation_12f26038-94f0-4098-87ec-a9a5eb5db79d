package com.maguo.loan.cash.flow.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * @Classname IsRightsPay
 * @Description TODO
 * @Date 2023/10/13 17:08
 * @Created by gale
 */
public enum IsRightsPay {

    Y("1", "已支付"),
    N("0", "未支付");

    private String code;
    private String desc;

    IsRightsPay(String code, String desc) {

        this.code = code;
        this.desc = desc;

    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static IsRightsPay getName(String name) {
        for (IsRightsPay value : IsRightsPay.values()) {
            if (StringUtils.equals(value.name(), name)) {
                return value;
            }
        }
        return IsRightsPay.N;
    }
}
