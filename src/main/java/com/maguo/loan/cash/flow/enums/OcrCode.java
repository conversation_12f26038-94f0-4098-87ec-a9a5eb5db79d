package com.maguo.loan.cash.flow.enums;

public enum OcrCode {

    CODE_SUCCESS("调用成功", 0000),
    CODE_VERIFY_FAIL("验签失败", 1000),
    CODE_PARAMETER_NOT_UTF8("参数非UTF-8编码", 1001),
    CODE_REQUEST_PARAMETER_ERROR("请求参数错误", 1002),
    CODE_MERCHANT_FREEZE("商户冻结", 1003),
    CODE_MERCHANT_DISABLE("商户停用", 1004),
    CODE_MERCHANT_ACCOUNT_EXPIRED("商户账号过期", 1005),
    CODE_CALL_FREQUENCY_HIGH("调用频率过高", 1006),
    CODE_FREE_TIMES_EXHAUSTED("免费次数用尽", 1007),
    CODE_SERVICE_NO_PERMISSION("服务无权限", 1008),
    CODE_APP_KEY_VERIFY_FAIL("App_key验证失败", 1009),
    CODE_REQUEST_EXPIRED("请求过期", 1010),
    CODE_SUPPLIER_SERVICE_EXCEPTION("供应商服务异常", 2000),
    CODE_OTHER_ERROR("其他错误", 9999);

    private String msg;
    private int code;

    OcrCode(String msg, int code) {
        this.msg = msg;
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public int getCode() {
        return code;
    }

    public static OcrCode fromCode(int code) {
        for (OcrCode ocrCode : OcrCode.values()) {
            if (ocrCode.getCode() == code) {
                return ocrCode;
            }
        }
        throw new IllegalArgumentException("Invalid  OCR  code:  " + code);
    }
}
