/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/6
 * @describe XXL
 */
package com.maguo.loan.cash.flow.enums;

/**
 * OCR类型：身份证，银行卡，人脸，防HACK
 */
public enum OcrType {
    ID_CARD("身份证", "https://api.lfv2.cn/api/ocr/idcard"),
    BANK_CARD("银行卡", "https://api.lfv2.cn/api/ocr/bankcard"),
    FACE("人脸", "https://api.lfv2.cn/api/identity/portrait_verification"),
    HACK("防HACK", "https://api.lfv2.cn/api/hack/image"),
    LIVENESS("liveness", "https://api.lfv2.cn/api/auth/get_token");

    private String desc;
    private String url;

    OcrType(String desc, String url) {
        this.desc = desc;
        this.url = url;
    }

    public String getDesc() {
        return desc;
    }

    public String getUrl() {
        return url;
    }


}
