package com.maguo.loan.cash.flow.enums;

/**
 * <AUTHOR>
 * @date 2023/9/21
 */
public enum OrderState {
    INIT("初始化"),
    AUDIT_PASS("平台风控通过"),
    CREDITING("授信中"),
    CREDIT_PASS("授信通过"),
    CREDIT_FAIL("授信失败"),
    LOANING("放款中"),
    LOAN_PASS("放款通过"),
    LOAN_FAIL("放款失败"),
    //挂起
    SUSPENDED("订单挂起"),
    CLEAR("结清"),
    LOAN_CANCEL("放款取消");

    private String desc;

    OrderState(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByType(String type) {
        try {
            return OrderState.valueOf(type).getDesc();
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
            return null;  // 或抛出自定义异常
        }
    }

}
