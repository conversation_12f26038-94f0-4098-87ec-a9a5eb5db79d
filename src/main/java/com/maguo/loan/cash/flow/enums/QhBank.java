package com.maguo.loan.cash.flow.enums;



import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.BindSignMode;

import java.math.BigDecimal;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/1/2
 */
public enum QhBank {

    CYBK(new BigDecimal("0.239900"), new BigDecimal("0.1500"), BankChannel.CYBK, BindSignMode.SHARE, false, "长银消金", 5),
    HXBK(new BigDecimal("0.239900"), new BigDecimal("0.1500"), BankChannel.HXBK, BindSignMode.SHARE, false, "湖消", 3),

    ;


    /**
     * bank对客利率
     */
    private final BigDecimal bankCustomRate;
    /**
     * bank合同利率
     */
    private final BigDecimal bankRate;

    private final BankChannel bankChannel;
    /**
     * 绑卡签约模式 BIND：需要资方绑卡， SHARE：协议共享，不需要绑卡
     */
    private final BindSignMode bindSignMode;
    /**
     * 放款前是否需要 发送共享协议号
     */
    private final boolean needShareAgreeNo;

    /**
     * 银行名称
     */
    private final String bankName;

    /**
     * 宽限期
     */
    private final int graceDay;

    QhBank(BigDecimal bankCustomRate, BigDecimal bankRate, BankChannel bankChannel, BindSignMode bindSignMode, boolean needShareAgreeNo,
           String bankName, int graceDay) {
        this.bankCustomRate = bankCustomRate;
        this.bankRate = bankRate;
        this.bankChannel = bankChannel;
        this.bindSignMode = bindSignMode;
        this.needShareAgreeNo = needShareAgreeNo;
        this.bankName = bankName;
        this.graceDay = graceDay;
    }

    public static QhBank getQhBankBy(BankChannel bankChannel) {
        return Arrays.stream(values()).filter(q -> q.bankChannel.equals(bankChannel)).findFirst()
            .orElseThrow(() -> new RuntimeException("QhBank.getQhBankBy未找到适配的资方:" + bankChannel));
    }

    public BigDecimal getBankCustomRate() {
        return bankCustomRate;
    }

    public BigDecimal getBankRate() {
        return bankRate;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public BindSignMode getBindSignMode() {
        return bindSignMode;
    }

    public boolean isNeedShareAgreeNo() {
        return needShareAgreeNo;
    }

    public String getBankName() {
        return bankName;
    }

    public int getGraceDay() {
        return graceDay;
    }
}
