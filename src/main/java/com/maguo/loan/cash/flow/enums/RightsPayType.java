package com.maguo.loan.cash.flow.enums;


import com.jinghang.common.util.StringUtil;

/**
 * <AUTHOR>
 * @since 2025-02-14
 */
public enum RightsPayType {
    NORMAL,
    PERIOD,
    SUBSCRIBE;

    public static RightsPayType getRightsPayType(String rightsPayType) {
        if ("0".equals(rightsPayType)) {
            return SUBSCRIBE;
        } else if ("1".equals(rightsPayType)) {
            return NORMAL;
        } else {
            if (StringUtil.isNotBlank(rightsPayType) && Integer.parseInt(rightsPayType) >= 2) {
                return PERIOD;
            }
            return NORMAL;
        }
    }
}
