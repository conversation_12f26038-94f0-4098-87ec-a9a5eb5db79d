package com.maguo.loan.cash.flow.enums;

/**
 * <AUTHOR>
 * @date 2024/4/23
 */
public enum RiskLevelEnum {

    BOTTOM_RIGHTS_CONFIG(-1, "兜底权益");

    private Integer riskLevel;

    private String description;

    RiskLevelEnum(Integer riskLevel, String description) {
        this.riskLevel = riskLevel;
        this.description = description;
    }

    public Integer getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(Integer riskLevel) {
        this.riskLevel = riskLevel;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
