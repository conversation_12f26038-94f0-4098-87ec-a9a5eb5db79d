package com.maguo.loan.cash.flow.enums;



import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;

import java.util.Objects;

/**
 * @Classname SendMsgScene
 * @Description 短信发送场景
 * @Date 2023/10/12 20:20
 * @Created by gale
 */
public enum SendMsgScene {

    LOGIN("1", "QH036", "登陆注册"),
    RESET("2", "QH053", "密码重置");

    private final String code;
    private final String value;

    private final String desc;

    SendMsgScene(String code, String value, String desc) {
        this.code = code;
        this.value = value;
        this.desc = desc;
    }

    public static SendMsgScene getSendMsgScene(String code) {
        for (SendMsgScene value : SendMsgScene.values()) {
            if (Objects.equals(value.code, code)) {
                return value;
            }
        }
        throw new BizException(ResultCode.SEND_MSG_SCENE_NOT_SUPPORTED);
    }


    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
