package com.maguo.loan.cash.flow.job.fql;

import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.entity.fql.FqlLoanVo;
import com.maguo.loan.cash.flow.entrance.fql.config.FqlConfig;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.job.jh.LoanJhJob;
import com.maguo.loan.cash.flow.service.FqlReconService;
import com.maguo.loan.cash.flow.util.SftpUtils;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Component
@JobHandler("FqlLoanJob")
public class FqlLoanJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(LoanJhJob.class);

    @Autowired
    private FqlReconService fqlReconService;
    @Autowired
    private SftpUtils sftpUtils;
    @Autowired
    private FqlConfig fqlConfig;

    @Override
    public void doJob( JobParam jobParam ) {
        // 参数要传 {"channel":"LVXIN","bankChannel":"CYBK/HXBK"}
        logger.info("生成分期乐放款明细对账文件csv开始");
        try {
            // 优先从 JobParam 获取 startDate，如果没有则使用当前日期减一天
            LocalDate localDate;
            if ( Objects.nonNull(jobParam) && jobParam.getStartDate() != null) {
                localDate = jobParam.getStartDate();
            } else {
                localDate = LocalDate.now().minusDays(1);
            }
            String  yesterday = localDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String fileName = "payment_" + yesterday + ".txt";
            String okFileName = "payment_" + yesterday + ".ok";
            String remoteDir = fqlConfig.getFqlLoanSftpPath() + yesterday + "/";
            FlowChannel flowChannel = FlowChannel.getFlowChannel(jobParam.getChannel());
            logger.info("获取分期乐放款成功文件数据 时间: {}, 流量渠道: {}, 资金渠道: {}", localDate, flowChannel, jobParam.getBankChannel());
            // 获取分期乐客户数据
            List<FqlLoanVo> loanVos = fqlReconService.getFqlLoanDetailReconFile(localDate,flowChannel,jobParam.getBankChannel());

            // 生成内存中的csv文件 字节流
            ByteArrayOutputStream stream = generateCsvToStream(loanVos);

            // 上传到 SFTP 长银沿用之前的
            if ( jobParam.getBankChannel() == BankChannel.CYBK ) {
                sftpUtils.fqlUploadStreamToSftp(stream, fileName, remoteDir);
                // 上传.ok文件
                sftpUtils.fqlUploadStreamToSftp(new ByteArrayOutputStream(), okFileName, remoteDir);
                logger.info("分期乐-长银放款明细csv上传成功");
            }
        } catch (Exception e) {
            logger.error("分期乐放款明细csv上传失败:{} 资方渠道: {}", e, jobParam.getBankChannel());
            e.printStackTrace();
        }


    }


    private static ByteArrayOutputStream generateCsvToStream(List<FqlLoanVo> data) throws IOException {
        String[] headers = {
            "资产号", "借据号", "付款时间", "付款金额", "付款期数", "付款状态", "备用字段"
        };

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        OutputStreamWriter writer = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);
        BufferedWriter bw = new BufferedWriter(writer);

        // 写表头
        bw.write(String.join(",", headers));
        bw.newLine();

        // 写数据（你替换的逻辑）
        for (FqlLoanVo loan : data) {
            bw.write(String.join(",", Arrays.asList(
                safe(loan.getOuterLoanId()), safe(loan.getLoanId()), safe(loan.getLoanTime()),
                safe(loan.getAmount()), safe(String.valueOf(loan.getPeriods())), safe("0"))));
            bw.newLine();
        }

        bw.flush();
        return outputStream;
    }

    // 处理 null 的字段
    private static String safe(String val) {
        return val == null ? "" : val;
    }

    private static String safe( BigDecimal val ) {
        return val == null ? "" : val.toPlainString();
    }

}
