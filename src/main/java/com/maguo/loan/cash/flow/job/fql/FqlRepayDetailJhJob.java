package com.maguo.loan.cash.flow.job.fql;

import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.entity.fql.FqlRepayDetailVo;
import com.maguo.loan.cash.flow.entrance.fql.config.FqlConfig;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.IsIncludingEquity;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.service.FqlReconService;
import com.maguo.loan.cash.flow.util.SftpUtils;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @公司 中数金智(上海)有限公司
 * @时间 2025/08/11 17:00
 */
@Component
@JobHandler("FqlRepayDetailJhJob")
public class FqlRepayDetailJhJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(FqlRepayDetailJhJob.class);

    @Autowired
    private FqlReconService fqlReconService;
    @Autowired
    private SftpUtils sftpUtils;
    @Autowired
    private FqlConfig fqlConfig;

    @Override
    public void doJob( JobParam jobParam ) {
        // 参数要传 {"channel":"FQL","bankChannel":"CYBK/HXBK"}
        logger.info("生成分期乐还款明细csv文件开始");
        try {
            // 优先从 JobParam 获取 startDate，如果没有则使用当前日期减一天
            LocalDate localDate;
            if ( Objects.nonNull(jobParam) && jobParam.getStartDate() != null) {
                localDate = jobParam.getStartDate();
            } else {
                localDate = LocalDate.now().minusDays(1);
            }
            String  yesterday = localDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String fileName = "repayment_back_" + yesterday + ".txt";
            String okFileName = "repayment_back_" + yesterday + ".ok";
            String remoteDir = null;
            if ( jobParam.getBankChannel() == BankChannel.CYBK ) {
                remoteDir = fqlConfig.getFqlLoanSftpPath() + yesterday + "/";
            }
            FlowChannel flowChannel = FlowChannel.getFlowChannel(jobParam.getChannel());
            logger.info("获取分期乐还款明细数据 时间: {}, 流量渠道: {}, 资金渠道: {}", localDate, flowChannel, jobParam.getBankChannel());
            // 获取数据
            List<FqlRepayDetailVo> fqlRepayDetailVos = fqlReconService.getFqlRepayDetailReconFile(localDate,flowChannel,jobParam.getBankChannel(),IsIncludingEquity.N);

            // 生成csv文件流
            ByteArrayOutputStream stream = generateCsvToStream(fqlRepayDetailVos);

            // 上传到 SFTP 长银沿用之前的 湖消新增
            if ( jobParam.getBankChannel() == BankChannel.CYBK ) {
                sftpUtils.fqlUploadStreamToSftp(stream, fileName, remoteDir);
                // 上传.ok文件
                sftpUtils.fqlUploadStreamToSftp(new ByteArrayOutputStream(), okFileName, remoteDir);
                logger.info("分期乐-长银还款明细csv上传成功");
            }

        } catch ( Exception e ) {
            logger.error("分期乐还款明细csv上传失败", e);
            e.printStackTrace();
        }
    }

    private static ByteArrayOutputStream generateCsvToStream(List<FqlRepayDetailVo> data) throws IOException {
        String[] headers = {
            "资产号", "借据号", "结算日", "当前期数", "还款类型", "还款总额",
            "还款本金", "还款利息", "还款罚息","备用字段"
        };

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        OutputStreamWriter writer = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);
        BufferedWriter bw = new BufferedWriter(writer);

        // 写表头
        bw.write(String.join(",", headers));
        bw.newLine();

        // 写数据
        for (FqlRepayDetailVo loan : data) {
            bw.write(String.join(",", Arrays.asList(
                safe(loan.getOuterRepayNo()), safe(loan.getLoanId()), safe(loan.getRepaidDate()),
                safe(String.valueOf(loan.getPeriod())), safe(String.valueOf(loan.getRepayType())), safe(loan.getTotalAmt()),
                safe(loan.getPrincipalAmt()), safe(loan.getInterestAmt()), safe(loan.getPenaltyAmt())
            )));
            bw.newLine();
        }

        bw.flush();
        return outputStream;
    }


    // 处理 null 的字段
    private static String safe(String val) {
        return val == null ? "" : val;
    }

    private static String safe( BigDecimal val ) {
        return val == null ? "" : val.toPlainString();
    }

}
