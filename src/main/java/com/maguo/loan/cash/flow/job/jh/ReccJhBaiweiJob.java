package com.maguo.loan.cash.flow.job.jh;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.recc.ReccFileApplyDto;
import com.jinghang.capital.api.dto.recc.ReccFileDto;
import com.jinghang.capital.api.dto.recc.ReccFileListDto;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.entrance.fql.config.BaiWeiConfig;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.remote.core.FinReccService;
import com.maguo.loan.cash.flow.service.FileService;
import com.maguo.loan.cash.flow.util.SftpUtils;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.maguo.loan.cash.flow.job.jh.LoanJob
 * @标题 对账文件上传-百维
 * @作者 Mr.sandman
 * @时间 2025/05/29 15:48
 */
@Component
@JobHandler("reccJhBaiweiJob")
public class ReccJhBaiweiJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(ReccJhBaiweiJob.class);

    @Value(value = "${oss.bucket.name}")
    private String ossBucket;
    @Autowired
    private FileService fileService;
    @Autowired
    private FinReccService finReccService;
    @Autowired
    private SftpUtils sftpUtils;
    @Autowired
    private BaiWeiConfig baiWeiConfig;

    @Override
    public void doJob(JobParam jobParam) {
        //参数要传 {"channel":"FQLQY001","bankChannel":"CYBK"}
        logger.info("获取分期乐对账文件csv开始");
        try {
            //优先从 JobParam 获取 startDate，如果没有则使用当前日期减一天
            LocalDate localDate;
            if (Objects.nonNull(jobParam) && jobParam.getStartDate() != null) {
                localDate = jobParam.getStartDate();
            } else {
                localDate = LocalDate.now().minusDays(1);
            }
            String yesterday = localDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            ReccFileApplyDto fileApply = new ReccFileApplyDto();
            fileApply.setChannel(BankChannel.CYBK);
            fileApply.setFileDate(localDate);
            fileApply.setCompany(GuaranteeCompany.HYRD);
            RestResult<ReccFileListDto> restResult = finReccService.fileApply(fileApply);
            if (restResult.getData() == null || CollectionUtil.isEmpty(restResult.getData().getReccFiles())) {
                logger.info("获取昊悦融担，长银对账文件为空，fileDate:{}, company:{}, channel:{}",
                    fileApply.getFileDate(), fileApply.getCompany(), fileApply.getChannel());
                return;
            }
            List<ReccFileDto> reccFiles = restResult.getData().getReccFiles();
            logger.info("获取昊悦融担，长银对账文件数: {}", reccFiles.size());

//            ReccFileDto fileDto = new ReccFileDto();
//            fileDto.setFileDate(LocalDate.of(2026,12,14));
//            fileDto.setOssBucket("test-maguo01");
//            fileDto.setOssKey("cybk/recc/loan/********/loan_********.csv");
//            fileDto.setFileName("loan_********.csv");
//            fileDto.setReccDate(LocalDate.of(2025, 7,2));
//            fileDto.setChannel("CYBK");
//            fileDto.setReccType("LOAN_FILE");

//            ReccFileDto fileDto = new ReccFileDto();
//            fileDto.setFileDate(LocalDate.of(2026,12,14));
//            fileDto.setOssBucket("test-maguo01");
//            fileDto.setOssKey("cybk/recc/repayPlan/********/repayment_plan_********.csv");
//            fileDto.setFileName("repayment_plan_********.csv");
//            fileDto.setReccDate(LocalDate.of(2025, 7,2));
//            fileDto.setChannel("CYBK");
//            fileDto.setReccType("REPAYMENT_DETAIL_FILE");
//            List<ReccFileDto> reccFiles = new ArrayList<>(List.of(fileDto));

            for (ReccFileDto file : reccFiles) {
                logger.info("获取对账文件类型: {}", file.getReccType());
                if (StringUtil.isBlank(file.getOssKey())) {
                    logger.error("获取对账文件ossKey为空,对账文件名: {}", file.getFileName());
                    continue;
                }
                if (jobParam.getBankChannel() == BankChannel.CYBK) {
                    fileService.getOssFile(file.getOssBucket(), file.getOssKey(), inputStream -> {
                        try {
                            /**
                             * LOAN_FILE 长银借据明细文件
                             * REPAYMENT_FILE 长银还款明细文件
                             * REPAYMENT_DETAIL_FILE 长银还款后还款计划
                             * PLAN_FILE 长银还款计划文件
                             */
                            String fileName = file.getFileName();
                            String okFileName = fileName + ".ok";
                            //上传csv文件
                            sftpUtils.uploadStreamToBaiweiSftp(inputStream, fileName, baiWeiConfig.getAgreementSftpPath(yesterday, yesterday));
                            //上传.ok文件
                            sftpUtils.baiWeiUploadImgStreamToSftp(new ByteArrayOutputStream(), okFileName, baiWeiConfig.getAgreementSftpPath(yesterday, yesterday));
                        } catch (Exception e) {
                            logger.error("协议文件上传百维sftp失败:", e);
                            throw new RuntimeException(e);
                        }
                    });
                    logger.info("分期乐-长银对账文件{}上传百维成功", file.getFileName());
                }
            }

        } catch (Exception e) {
            logger.error("分期乐对账文件csv上传百维失败:{} 资方渠道: {}", e, jobParam.getBankChannel());
            e.printStackTrace();
        }
    }


}
