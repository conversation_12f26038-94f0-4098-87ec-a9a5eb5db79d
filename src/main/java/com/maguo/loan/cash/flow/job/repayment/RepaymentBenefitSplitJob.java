package com.maguo.loan.cash.flow.job.repayment;

import com.jinghang.capital.api.dto.RepayStatus;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.ppd.RepayBenefitAllocateRecord;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.fql.RepayBenefitAllocateRecordRepository;
import com.maguo.loan.cash.flow.util.DateTimeUtils;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 分润逻辑 实还金额(除本金外)分润逻辑 除本金外所有费用均按照以下规则分账:
 * 1、给长银侧的金额: 实还金额(除本金外)的0.25(即:6/24)
 * 2、给昊悦侧的金额: 实还金额(除本金外)的0.375(即:9/24)
 * 【注】上述俩金额需在客户实还时记录在还款记录表中
 *
 * <AUTHOR>
 * @date 2025-08-11 14:34
 */
@Component
@JobHandler("repaymentBenefitSplitJob")
public class RepaymentBenefitSplitJob extends AbstractJobHandler {

    private static final Logger log = LoggerFactory.getLogger(RepaymentBenefitSplitJob.class);
    private static final String CYBK = "CYBK";
    private static final String HAOYUE = "HAOYUE";


    @Autowired
    private CustomRepayRecordRepository customRepayRecordRepository;
    @Autowired
    private RepayBenefitAllocateRecordRepository repayBenefitAllocateRecordRepository;

    @Override
    public void doJob(JobParam jobParam) {
        // 1. 查询已还款但未分润的记录 时间区间内的数据
        DateTimeUtils.DateRange dateRange = DateTimeUtils.processDateParameters(jobParam);
        List<CustomRepayRecord> records = customRepayRecordRepository.findByProcessStatusAndTime(ProcessState.SUCCESS,
            dateRange.getStartDateTime(),
            dateRange.getEndDateTime());

        for (CustomRepayRecord record : records) {
            try {
                // 2. 检查是否已存在分润记录
                if (repayBenefitAllocateRecordRepository.findByCustomerRepayRecordId(record.getId())) {
                    log.warn("还款记录ID:{} 已存在分润记录，跳过处理", record.getId());
                    continue;
                }

                // 3. 计算非本金总金额和各分项金额
                NonPrincipalAmounts amounts = calculateNonPrincipalAmounts(record);

                // 4. 为长银和昊悦创建分润记录
                createDistributionRecord(record, CYBK, amounts);
                createDistributionRecord(record, HAOYUE, amounts);

                log.info("分润成功 - 还款记录ID:{}", record.getId());
            } catch (Exception e) {
                log.error("分润失败 - 还款记录ID:{}", record.getId(), e);
            }
        }

    }

    // 计算非本金各分项金额
    private NonPrincipalAmounts calculateNonPrincipalAmounts(CustomRepayRecord record) {
        NonPrincipalAmounts amounts = new NonPrincipalAmounts();

        // 总非本金 = 总金额 - 本金
        BigDecimal principal = record.getPrincipalAmt() != null ?
            record.getPrincipalAmt() : BigDecimal.ZERO;
        BigDecimal total = record.getTotalAmt() != null ?
            record.getTotalAmt() : BigDecimal.ZERO;
        amounts.totalNonPrincipal = total.subtract(principal);

        // 各分项金额
        amounts.interestAmt = record.getInterestAmt() != null ? record.getInterestAmt() : BigDecimal.ZERO;
        amounts.penaltyAmt = record.getPenaltyAmt() != null ? record.getPenaltyAmt() : BigDecimal.ZERO;
        amounts.guaranteeAmt = record.getGuaranteeAmt() != null ? record.getGuaranteeAmt() : BigDecimal.ZERO;
        amounts.consultFee = record.getConsultFee() != null ? record.getConsultFee() : BigDecimal.ZERO;
        amounts.extraGuaranteeAmt = record.getExtraGuaranteeAmt() != null ? record.getExtraGuaranteeAmt() : BigDecimal.ZERO;
        amounts.breachAmt = record.getBreachAmt() != null ? record.getBreachAmt() : BigDecimal.ZERO;

        return amounts;
    }

    // 创建分润记录
    private void createDistributionRecord(CustomRepayRecord record, String profitParty, NonPrincipalAmounts amounts) {
        RepayBenefitAllocateRecord benefitAllocateRecord = new RepayBenefitAllocateRecord();

        // 基础信息
        benefitAllocateRecord.setId(generateId());
        benefitAllocateRecord.setCustomerRepayRecordId(record.getId());
        benefitAllocateRecord.setLoanId(record.getLoanId());
        benefitAllocateRecord.setPeriod(record.getPeriod());
        benefitAllocateRecord.setOuterRepayNo(record.getOuterRepayNo());
        benefitAllocateRecord.setRepaidDate(record.getRepaidDate());
        benefitAllocateRecord.setProfitParty(profitParty);

        // 分润计算
        BigDecimal ratio = CYBK.equals(profitParty) ? new BigDecimal("0.25") : new BigDecimal("0.375");

        benefitAllocateRecord.setTotalNonPrincipal(amounts.totalNonPrincipal);
        benefitAllocateRecord.setTotalAmount(amounts.totalNonPrincipal.multiply(ratio));

        // 各分项分润金额
        benefitAllocateRecord.setInterestAmt(amounts.interestAmt.multiply(ratio));
        benefitAllocateRecord.setPenaltyAmt(amounts.penaltyAmt.multiply(ratio));
        benefitAllocateRecord.setGuaranteeAmt(amounts.guaranteeAmt.multiply(ratio));
        benefitAllocateRecord.setConsultFee(amounts.consultFee.multiply(ratio));
        benefitAllocateRecord.setExtraGuaranteeAmt(amounts.extraGuaranteeAmt.multiply(ratio));
        benefitAllocateRecord.setBreachAmt(amounts.breachAmt.multiply(ratio));

        // 剩余金额(只在最后一个分润方记录)
        if (HAOYUE.equals(profitParty)) {
            BigDecimal cybkAmount = amounts.totalNonPrincipal.multiply(new BigDecimal("0.25"));
            BigDecimal haoyueAmount = amounts.totalNonPrincipal.multiply(new BigDecimal("0.375"));
            benefitAllocateRecord.setRemainingAmount(
                amounts.totalNonPrincipal.subtract(cybkAmount).subtract(haoyueAmount)
            );
        } else {
            benefitAllocateRecord.setRemainingAmount(BigDecimal.ZERO);
        }
        benefitAllocateRecord.setStartRepayTime(record.getCreatedTime());

        repayBenefitAllocateRecordRepository.save(benefitAllocateRecord);
    }

    // 更新原还款记录状态
    private void updateOriginalRecord(CustomRepayRecord record) {
//        record.setstat("PROCESSED");
        record.setUpdatedTime(LocalDateTime.now());
        record.setUpdatedBy("SYSTEM_AUTO_DISTRIBUTION");
        customRepayRecordRepository.save(record);
    }

    // 处理分润失败
    private void handleFailedDistribution(CustomRepayRecord record, String errorMsg) {
        try {
            // 可以为每个分润方创建失败记录
            Arrays.asList(CYBK, HAOYUE).forEach(party -> {
                RepayBenefitAllocateRecord failedRecord = new RepayBenefitAllocateRecord();
                failedRecord.setId(generateId());
                failedRecord.setCustomerRepayRecordId(record.getId());
                failedRecord.setLoanId(record.getLoanId());
                failedRecord.setPeriod(record.getPeriod());
                failedRecord.setProfitParty(party);
                failedRecord.setAuditStatus("FAILED");
                repayBenefitAllocateRecordRepository.save(failedRecord);
            });

            // 标记原记录为处理失败
//            record.setDistributionStatus("FAILED");
//            customRepayRecordRepository.save(record);
        } catch (Exception ex) {
            log.error("保存失败分润记录异常", ex);
        }
    }

    private String generateId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    // 非本金各分项金额容器类
    private static class NonPrincipalAmounts {
        BigDecimal totalNonPrincipal;
        BigDecimal interestAmt;
        BigDecimal penaltyAmt;
        BigDecimal guaranteeAmt;
        BigDecimal consultFee;
        BigDecimal extraGuaranteeAmt;
        BigDecimal breachAmt;
    }


}

