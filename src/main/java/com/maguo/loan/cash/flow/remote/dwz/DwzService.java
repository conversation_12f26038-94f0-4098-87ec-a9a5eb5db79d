package com.maguo.loan.cash.flow.remote.dwz;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.jinghang.common.http.HttpFramework;
import com.jinghang.common.http.HttpRequest;
import com.jinghang.common.http.exception.HttpException;
import com.jinghang.common.util.HttpUtil;
import com.jinghang.common.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Classname DwzService
 * @Description 短网址 server
 * @Date 2023/11/1 18:10
 * @Created by gale
 */
@Service
public class DwzService {
    private static final Logger logger = LoggerFactory.getLogger(DwzService.class);

    @Value("${baidu.config.dwz:79fa1d987ac21a5facdd57f4cae02dec}")
    private String baiduDwzToken;


    /**
     * {\"Code\":-99,\"ShortUrls\":[{\"Code\":-11,\"LongUrl\":\"https://www.baidu.com/aa\"
     * ,\"ErrMsg\":\"不支持该域名的长网址\"}],\"ErrMsg\":\"exist failure\"}
     * <p>
     * {\"Code\":0,\"ShortUrls\":[{\"ShortUrl\":\"https://dwz.cn/BcZBWDRP\",\"LongUrl\":\"https://www.sina.com\"}]}
     *
     * @param longUrl
     * @return
     */
    public String getdwzUrl(String longUrl) {

        List<Map> list = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        map.put("LongUrl", longUrl);
        map.put("TermOfValidity", "1-year");
        list.add(map);

        HttpRequest httpRequest = null;
        try {
            httpRequest = HttpRequest.post(HttpFramework.HTTPCLIENT5, "https://dwz.cn/api/v3/short-urls")
                    .addHeader("Dwz-Token", baiduDwzToken)
                    .addHeader("Content-Type", "application/json; charset=UTF-8")
                    .addHeader("Content-Language", "zh")
                    .body(JsonUtil.convertToString(list));
            String result = HttpUtil.exec(httpRequest);
            logger.info("百度短链服务请求返回结果={}", JSON.toJSONString(result));
            JSONObject jsonResult = JSONObject.parseObject(result);
            if (jsonResult.getInteger("Code").equals(0)) {
                return jsonResult.getJSONArray("ShortUrls").getJSONObject(0).getString("ShortUrl");
            }
        } catch (HttpException | JsonProcessingException e) {
            logger.error("百度短链服务请求失败=", e);
        }

        return "";
    }


}
