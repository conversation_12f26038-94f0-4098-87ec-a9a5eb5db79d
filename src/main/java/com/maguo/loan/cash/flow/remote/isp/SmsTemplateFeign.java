package com.maguo.loan.cash.flow.remote.isp;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * isp http 调用走本地
 * TODO 需要抽出来做配置  其它异常机制暂不考虑
 */
@FeignClient(name = "isp", url="127.0.0.1:8233")
public interface SmsTemplateFeign {

    /**
     *
     * @param params 手机号(phone) 模板ID(templateId) +  业务参数其它 根据报错信息获取
     * @return 0 成功 其它失败
     */
    @PostMapping("/msg/security/code")
    Map<String, Object> sendSms(@RequestBody Map<String,Object> params);

}
