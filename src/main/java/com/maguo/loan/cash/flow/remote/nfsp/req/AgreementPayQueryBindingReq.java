package com.maguo.loan.cash.flow.remote.nfsp.req;


import com.maguo.loan.cash.flow.remote.nfsp.CommonRequestType;

public class AgreementPayQueryBindingReq extends CommonBaseReq {
    /**
     * 协议号
     */
    private String withholdAgreementNo;
    private String clientId;

    public String getWithholdAgreementNo() {
        return withholdAgreementNo;
    }

    public void setWithholdAgreementNo(String withholdAgreementNo) {
        this.withholdAgreementNo = withholdAgreementNo;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    @Override
    public CommonRequestType getRequestType() {
        return CommonRequestType.QUERY_BINDING;
    }
}
