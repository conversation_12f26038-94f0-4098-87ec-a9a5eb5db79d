package com.maguo.loan.cash.flow.remote.nfsp.req;


import com.maguo.loan.cash.flow.remote.nfsp.CommonRequestType;

/**
 * <AUTHOR>
 * @date 2023/5/31
 */
public class AgreementSignQuery extends CommonBaseReq {
    private String taskId;

    private String userId;

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    @Override
    public CommonRequestType getRequestType() {
        return CommonRequestType.AGREEMENT_SIGN_RESULT;
    }
}
