package com.maguo.loan.cash.flow.remote.nfsp.req;



import com.maguo.loan.cash.flow.remote.nfsp.CommonRequestType;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/31
 */
public class AgreementSignReq extends CommonBaseReq {
    private String templateNo;

    private String personName;

    private String address;

    private String bankMobilePhone;

    private String mobilePhone;

    private String identNo;

    private String identTypeCode;

    private String signatureImageData;

    private Map<String, String> params;

    private List<String> identitys;

    public String getTemplateNo() {
        return templateNo;
    }

    public void setTemplateNo(String templateNo) {
        this.templateNo = templateNo;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getBankMobilePhone() {
        return bankMobilePhone;
    }

    public void setBankMobilePhone(String bankMobilePhone) {
        this.bankMobilePhone = bankMobilePhone;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getIdentNo() {
        return identNo;
    }

    public void setIdentNo(String identNo) {
        this.identNo = identNo;
    }

    public String getIdentTypeCode() {
        return identTypeCode;
    }

    public void setIdentTypeCode(String identTypeCode) {
        this.identTypeCode = identTypeCode;
    }

    public String getSignatureImageData() {
        return signatureImageData;
    }

    public void setSignatureImageData(String signatureImageData) {
        this.signatureImageData = signatureImageData;
    }

    public Map<String, String> getParams() {
        return params;
    }

    public void setParams(Map<String, String> params) {
        this.params = params;
    }

    public List<String> getIdentitys() {
        return identitys;
    }

    public void setIdentitys(List<String> identitys) {
        this.identitys = identitys;
    }

    @Override
    public CommonRequestType getRequestType() {
        return CommonRequestType.AGREEMENT_SIGN;
    }
}
