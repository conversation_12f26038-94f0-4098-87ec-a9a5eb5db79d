package com.maguo.loan.cash.flow.remote.nfsp.req;


import com.maguo.loan.cash.flow.remote.nfsp.CommonRequestType;

/**
 * 绑卡申请
 */
public class BindApplyReq extends CommonBaseReq {


    /**
     * 卡号
     */
    private String bankCardNo;
    /**
     * 证件号码
     */
    private String certNo;

    /**
     * 预留手机号码
     */
    private String phone;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 卡类型（1:借记卡；2:信用卡）默认“1”
     */
    private String cardType = "1";
    /**
     * 银行代码（如：ICBC，ABC）
     */
    private String code;

    /**
     * 支付类型（JINGDONG：京东支付；BAOFOO：宝付支付；YEEPAY：易宝支付）
     */
    private String payType;

    /**
     * 支付渠道二级商户号（522233）
     */
    private String merchantNo;

    /**
     * 商户绑卡模式
     */
    private String bindMode;

    public String getBindMode() {
        return bindMode;
    }

    public void setBindMode(String bindMode) {
        this.bindMode = bindMode;
    }

    public String getBankCardNo() {
        return bankCardNo;
    }

    public void setBankCardNo(String bankCardNo) {
        this.bankCardNo = bankCardNo;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    @Override
    public CommonRequestType getRequestType() {
        return CommonRequestType.AGREEMENT_BIND_CARD_APPLY;
    }


}
