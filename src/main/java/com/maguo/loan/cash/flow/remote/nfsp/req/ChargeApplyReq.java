package com.maguo.loan.cash.flow.remote.nfsp.req;



import com.maguo.loan.cash.flow.remote.nfsp.CommonRequestType;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/6 14:03
 */
public class ChargeApplyReq extends CommonBaseReq {


    /**
     * 绑卡协议号
     */
    private String thirdPartyAgreementNo;
    /**
     * 收款主体
     */
    private String thirdPartyClientId;
    private BigDecimal withholdTradeAmount;
    private String withholdTradeNo;
    private String withholdTradeSubject;
    private String bizOrderNoPrefix;
    private String businessEntity;
    private String channelId;
    private String remark;

    /**
     * 业务订单规则: CUSTOMIZED/PREFIX"
     */
    private String bizOrderNoRule;

    private List<ShareInfo> shareInfo;
    /**
     * 分账手续费商户
     */
    private String feeMemberId;
    /**
     * 计费商户号
     */
    private String callFeeMemberId;

    /**
     * 确认子商户
     */
    private List<String> checkMemberInfo;

    public List<String> getCheckMemberInfo() {
        return checkMemberInfo;
    }

    public void setCheckMemberInfo(List<String> checkMemberInfo) {
        this.checkMemberInfo = checkMemberInfo;
    }

    public List<ShareInfo> getShareInfo() {
        return shareInfo;
    }

    public void setShareInfo(List<ShareInfo> shareInfo) {
        this.shareInfo = shareInfo;
    }

    public String getThirdPartyAgreementNo() {
        return thirdPartyAgreementNo;
    }

    public void setThirdPartyAgreementNo(String thirdPartyAgreementNo) {
        this.thirdPartyAgreementNo = thirdPartyAgreementNo;
    }

    public String getThirdPartyClientId() {
        return thirdPartyClientId;
    }

    public void setThirdPartyClientId(String thirdPartyClientId) {
        this.thirdPartyClientId = thirdPartyClientId;
    }

    public BigDecimal getWithholdTradeAmount() {
        return withholdTradeAmount;
    }

    public void setWithholdTradeAmount(BigDecimal withholdTradeAmount) {
        this.withholdTradeAmount = withholdTradeAmount;
    }

    public String getWithholdTradeNo() {
        return withholdTradeNo;
    }

    public void setWithholdTradeNo(String withholdTradeNo) {
        this.withholdTradeNo = withholdTradeNo;
    }

    public String getWithholdTradeSubject() {
        return withholdTradeSubject;
    }

    public void setWithholdTradeSubject(String withholdTradeSubject) {
        this.withholdTradeSubject = withholdTradeSubject;
    }

    public String getBizOrderNoPrefix() {
        return bizOrderNoPrefix;
    }

    public void setBizOrderNoPrefix(String bizOrderNoPrefix) {
        this.bizOrderNoPrefix = bizOrderNoPrefix;
    }

    public String getBusinessEntity() {
        return businessEntity;
    }

    public void setBusinessEntity(String businessEntity) {
        this.businessEntity = businessEntity;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getBizOrderNoRule() {
        return bizOrderNoRule;
    }

    public void setBizOrderNoRule(String bizOrderNoRule) {
        this.bizOrderNoRule = bizOrderNoRule;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getFeeMemberId() {
        return feeMemberId;
    }

    public void setFeeMemberId(String feeMemberId) {
        this.feeMemberId = feeMemberId;
    }

    public String getCallFeeMemberId() {
        return callFeeMemberId;
    }

    public void setCallFeeMemberId(String callFeeMemberId) {
        this.callFeeMemberId = callFeeMemberId;
    }

    @Override
    public CommonRequestType getRequestType() {
        return CommonRequestType.AGREEMENT_WITHHOLD;
    }

    public static class ShareInfo {

        private String shareMchId;
        private BigDecimal shareAmount;

        public String getShareMchId() {
            return shareMchId;
        }

        public void setShareMchId(String shareMchId) {
            this.shareMchId = shareMchId;
        }

        public BigDecimal getShareAmount() {
            return shareAmount;
        }

        public void setShareAmount(BigDecimal shareAmount) {
            this.shareAmount = shareAmount;
        }
    }


}
