package com.maguo.loan.cash.flow.remote.nfsp.req;


import com.maguo.loan.cash.flow.remote.nfsp.CommonRequestType;

/**
 * <AUTHOR>
 * @date 2023/6/6 14:08
 */
public class ChargeQueryReq extends CommonBaseReq {

    private String channelId;
    private String orderId;

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    @Override
    public CommonRequestType getRequestType() {
        return CommonRequestType.AGREEMENT_WITHHOLD_RESULT;
    }
}
