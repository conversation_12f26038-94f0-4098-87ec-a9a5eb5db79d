package com.maguo.loan.cash.flow.remote.nfsp.req;


import com.maguo.loan.cash.flow.remote.nfsp.CommonRequestType;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/6 14:08
 */
public class SmsSendReq extends CommonBaseReq {
    /**
     * 短信发送方式
     * <p>1直接内容发送,<br/>2通过配置的模版发送</p>
     */
    private String type;
    private String context;
    private String sign;
    /**
     * 模版ID编号，type为2时必填
     */
    private String templateId;
    /**
     * 短信内容参数列表
     */
    private Map<String, String> messageParamMap;
    /**
     * 短信发送手机号码列表数组
     */
    private List<String> phoneList;


    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public Map<String, String> getMessageParamMap() {
        return messageParamMap;
    }

    public void setMessageParamMap(Map<String, String> messageParamMap) {
        this.messageParamMap = messageParamMap;
    }

    public List<String> getPhoneList() {
        return phoneList;
    }

    public void setPhoneList(List<String> phoneList) {
        this.phoneList = phoneList;
    }

    @Override
    public CommonRequestType getRequestType() {
        return CommonRequestType.SMS_SEND;
    }
}
