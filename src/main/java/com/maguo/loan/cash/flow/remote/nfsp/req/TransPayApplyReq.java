package com.maguo.loan.cash.flow.remote.nfsp.req;


import com.maguo.loan.cash.flow.remote.nfsp.CommonRequestType;

/**
 * 转账支付申请
 */
public class TransPayApplyReq extends CommonBaseReq {

    /**
     * 收款主体
     */
    private String thirdPartyClientId;

    /**
     * 商户订单号
     */
    private String transId;

    /**
     * 支付金额
     * 单位：分
     */
    private String orderMoney;

    /**
     * 收款方账户号
     */
    private String payeeAcctCode;

    /**
     * 付款方账户号
     */
    private String payerAcctCode;

    /**
     * 付款方开户名
     */
    private String payerUserName;

    /**
     * 银行流水号
     */
    private String bankSerialNo;

    /**
     * 分账明细
     */
    private String shareInfo;

    /**
     * 手续费承担方
     */
    private String feeMemberId;


    public String getThirdPartyClientId() {
        return thirdPartyClientId;
    }

    public void setThirdPartyClientId(String thirdPartyClientId) {
        this.thirdPartyClientId = thirdPartyClientId;
    }

    public String getTransId() {
        return transId;
    }

    public void setTransId(String transId) {
        this.transId = transId;
    }

    public String getOrderMoney() {
        return orderMoney;
    }

    public void setOrderMoney(String orderMoney) {
        this.orderMoney = orderMoney;
    }

    public String getPayeeAcctCode() {
        return payeeAcctCode;
    }

    public void setPayeeAcctCode(String payeeAcctCode) {
        this.payeeAcctCode = payeeAcctCode;
    }

    public String getPayerAcctCode() {
        return payerAcctCode;
    }

    public void setPayerAcctCode(String payerAcctCode) {
        this.payerAcctCode = payerAcctCode;
    }

    public String getPayerUserName() {
        return payerUserName;
    }

    public void setPayerUserName(String payerUserName) {
        this.payerUserName = payerUserName;
    }

    public String getBankSerialNo() {
        return bankSerialNo;
    }

    public void setBankSerialNo(String bankSerialNo) {
        this.bankSerialNo = bankSerialNo;
    }

    public String getShareInfo() {
        return shareInfo;
    }

    public void setShareInfo(String shareInfo) {
        this.shareInfo = shareInfo;
    }

    public String getFeeMemberId() {
        return feeMemberId;
    }

    public void setFeeMemberId(String feeMemberId) {
        this.feeMemberId = feeMemberId;
    }

    @Override
    public CommonRequestType getRequestType() {
        return CommonRequestType.TRANS_PAY_APPLY;
    }


}
