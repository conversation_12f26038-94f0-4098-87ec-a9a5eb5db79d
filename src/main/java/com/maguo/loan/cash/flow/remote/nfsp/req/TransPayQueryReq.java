package com.maguo.loan.cash.flow.remote.nfsp.req;


import com.maguo.loan.cash.flow.remote.nfsp.CommonRequestType;

/**
 * 转账支付申请
 */
public class TransPayQueryReq extends CommonBaseReq {

    /**
     * 收款主体
     */
    private String thirdPartyClientId;

    /**
     * 商户订单号
     */
    private String transId;

    public String getThirdPartyClientId() {
        return thirdPartyClientId;
    }

    public void setThirdPartyClientId(String thirdPartyClientId) {
        this.thirdPartyClientId = thirdPartyClientId;
    }

    public String getTransId() {
        return transId;
    }

    public void setTransId(String transId) {
        this.transId = transId;
    }

    @Override
    public CommonRequestType getRequestType() {
        return CommonRequestType.TRANS_PAY_QUERY;
    }


}
