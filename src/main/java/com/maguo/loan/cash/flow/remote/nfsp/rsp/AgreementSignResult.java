package com.maguo.loan.cash.flow.remote.nfsp.rsp;

/**
 * <AUTHOR>
 * @date 2023/5/31
 */
public class AgreementSignResult extends StateAble {

    public static final String SUCCESS = "S";
    public static final String PROCESSING = "P";
    public static final String FAIL = "F";

    private String contractState;
    private String errorMessage;

    private String contractNo;
    private String ossContractFileKey;
    private String ossContractFileUrl;


    public String getContractState() {
        return contractState;
    }

    public void setContractState(String contractState) {
        this.contractState = contractState;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getOssContractFileKey() {
        return ossContractFileKey;
    }

    public void setOssContractFileKey(String ossContractFileKey) {
        this.ossContractFileKey = ossContractFileKey;
    }

    public String getOssContractFileUrl() {
        return ossContractFileUrl;
    }

    public void setOssContractFileUrl(String ossContractFileUrl) {
        this.ossContractFileUrl = ossContractFileUrl;
    }
}
