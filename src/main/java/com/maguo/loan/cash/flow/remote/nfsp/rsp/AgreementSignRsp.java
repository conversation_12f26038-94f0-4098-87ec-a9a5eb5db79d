package com.maguo.loan.cash.flow.remote.nfsp.rsp;

/**
 * <AUTHOR>
 * @date 2023/5/31
 */
public class AgreementSignRsp extends StateAble {

    private String taskId;

    private String userId;

    private String failMsg;

    public String getFailMsg() {
        return failMsg;
    }

    public void setFailMsg(String failMsg) {
        this.failMsg = failMsg;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}
