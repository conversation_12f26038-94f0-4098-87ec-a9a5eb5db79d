package com.maguo.loan.cash.flow.util;

import org.slf4j.MDC;
import org.springframework.core.task.TaskDecorator;

import java.util.UUID;

/**
 * <AUTHOR>
 */
public class TraceIdGenerator implements TaskDecorator {
    @Override
    public Runnable decorate(Runnable runnable) {
        return () -> {
            try {
                String traceId = UUID.randomUUID().toString().replace("-", "");
                String spanId = traceId.substring(0, 16);

                MDC.put("traceId", traceId);
                MDC.put("spanId", spanId);
                // 执行异步任务
                runnable.run();
            } finally {
                MDC.clear();
            }
        };
    }
}
