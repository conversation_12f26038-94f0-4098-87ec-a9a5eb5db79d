package com.sing.service.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 处理跨域请求
 * @公司 中数金智(上海)有限公司
 * @包名 com.post.server.config.CorsConfig
 * @作者 Mr.sandman
 * @时间 2023/06/13 13:57
 * @邮箱 <EMAIL>
 */
@Configuration
public class CorsConfig implements WebMvcConfigurer {
  @Override
  public void addCorsMappings( CorsRegistry registry ) {
    registry.addMapping("/**")//项目中的所有接口都支持跨域
        .allowedOriginPatterns("*")//所有地址都可以访问，也可以配置具体地址
        .allowCredentials(true)
        .allowedMethods("*")//"GET", "HEAD", "POST", "PUT", "DELETE", "OPTIONS"
        .maxAge(3600);// 跨域允许时间
  }
}
