package com.sing.service.config;

import com.sing.service.constant.ConfigConstant;
import com.timevale.esign.paas.tech.client.ServiceClient;
import com.timevale.esign.paas.tech.client.ServiceClientManager;
import com.timevale.esign.paas.tech.service.*;
import com.timevale.tech.sdk.bean.HttpConnectionConfig;
import com.timevale.tech.sdk.bean.ProjectConfig;
import com.timevale.tech.sdk.bean.SignatureConfig;
import com.timevale.tech.sdk.constants.AlgorithmType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.singservice.sing_service.config.EsignConfig
 * @作者 Mr.sandman
 * @时间 2025/05/20 15:16
 */
@Configuration
public class ESignConfig {

  @Value("${esign.project.id}")
  private String pojectId;
  @Value("${esign.project.secret}")
  private String pojectSecret;
  @Value("${esign.api.host}")
  private String apiHost;
  @Value("${esign.api.url}")
  private String apiUrl;

  @Bean
  public ServiceClient serviceClient() {
    try {
       ServiceClientManager.registClient(getProjectCfg(), getHttpConCfg(), getSignatureCfg());
       return ServiceClientManager.get(pojectId);
    } catch (Exception e) {
      throw new RuntimeException("客户端注册失败", e);
    }
  }
  @Bean
  public PdfDocumentService pdfDocumentService(ServiceClient client) {
    return client.pdfDocumentService();
  }
  @Bean
  public AccountService accountService( ServiceClient client ) {
    return client.accountService();
  }
  @Bean
  public PlatformSignService platformSignService( ServiceClient client ) {
    return client.platformSignService();
  }
  @Bean
  public SignVerifyService signVerifyService( ServiceClient client ) {
    return client.signVerifyService();
  }
  @Bean
  public TemplateSealService templateSealService( ServiceClient client ) {
    return client.templateSealService();
  }
  @Bean
  public UserSignService userSignService( ServiceClient client ) {
    return client.userSignService();
  }

  /**
   *
   * description 进行应用配置，如果是测试环境，请联系E签宝交付顾问获取
   */
  public ProjectConfig getProjectCfg() {
    ProjectConfig proCfg = new ProjectConfig();
    // 应用ID（应用ID）
    proCfg.setProjectId(pojectId);
    // 应用Secret(应用Secret)
    proCfg.setProjectSecret(pojectSecret);
    // 开放平台地址
    // 需要添加ip白名单，而且添加之后需要等五分钟之后才能生效，否则会报错：接口调用方尚未配置ip白名单，请联系e签宝管理员配置
    // 正式环境请求地址：http://sdkapi.esign.cn/tgmonitor/rest/app!getAPIInfo2
    // 测试环境请求地址：http://smlitsm.tsign.cn:8080/tgmonitor/rest/app!getAPIInfo2
    proCfg.setItsmApiUrl(apiHost + apiUrl);
    return proCfg;
  }

  /**
   *
   * description http配置
   */
  private static HttpConnectionConfig getHttpConCfg() {
    HttpConnectionConfig httpConCfg = new HttpConnectionConfig();
    // // 代理服务IP配置
    // httpConCfg.setProxyIp(null);
    // // 代理服务端口
    // httpConCfg.setProxyPort(null);
    // // 协议类型，默认Https
    // httpConCfg.setHttpType(HttpType.HTTPS);
    // // 请求失败重试次数，默认5次
    // httpConCfg.setRetry(null);
    // //连接超时时间配置，最大不能超过30秒
    // httpConCfg.setTimeoutConnect(30);
    // // 请求超时时间，最大不能超过30
    // httpConCfg.setTimeoutRequest(30);
    // // 代理服务器登录用户名
    // httpConCfg.setUsername(null);
    // // 代理服务器登录密码
    // httpConCfg.setPassword(null);
    return httpConCfg;
  }

  /**
   * description 签名配置
   */
  private static SignatureConfig getSignatureCfg() {
    SignatureConfig signCfg = new SignatureConfig();
    signCfg.setAlgorithm(AlgorithmType.HMACSHA256);

    //若算法类型是RSA，需要设置e签宝公钥和平台私钥
    if(AlgorithmType.RSA == ConfigConstant.ALGORITHM_TYPE){
      signCfg.setEsignPublicKey(ConfigConstant.ESIGN_PUB_KEY);
      signCfg.setPrivateKey(ConfigConstant.ESIGN_PRI_KEY);
    }

    return signCfg;
  }
}
