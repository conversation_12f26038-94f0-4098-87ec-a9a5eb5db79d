package com.sing.service.config;

import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.OpenAPI;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * @公司 中数金智(上海)有限公司
 */
@Configuration
public class Knife4jConfiguration {

  public static final String URL = "http://localhost:8081/doc.html";
  private static final String GROUP_NAME = "sing-service";
  private static final String BASE_PACKAGE = "com.sing.service.controller";

  @Bean
  public OpenAPI customOpenAPI() {
    return new OpenAPI()
        .info(new Info()
                  .title("Sing-Service API")
                  .version("2.0")
                  .description("统计Sing-Service相关的API")
                  .termsOfService(URL)
                  .contact(new Contact()
                               .name("Mr.sandman")
                               .email("<EMAIL>")));
  }

  @Bean
  public GroupedOpenApi publicApi() {
    return GroupedOpenApi.builder()
        .group(GROUP_NAME)
        .packagesToScan(BASE_PACKAGE)
        .pathsToMatch("/esign/**")  // 可选：限制API路径
        .build();
  }

}
