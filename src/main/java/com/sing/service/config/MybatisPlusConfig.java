package com.sing.service.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.post.server.config.MybatisPlusConfig
 * @作者 Mr.sandman
 * @时间 2023/06/08 11:29
 * @邮箱 <EMAIL>
 */
@Configuration
public class MybatisPlusConfig {

  /**
   * 新增分页拦截器，并设置数据库类型为mysql
   */
  @Bean
  public MybatisPlusInterceptor mybatisPlusInterceptor() {
    MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
    interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
    return interceptor;
  }
}
