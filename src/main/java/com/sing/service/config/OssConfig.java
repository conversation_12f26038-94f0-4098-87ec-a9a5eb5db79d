package com.sing.service.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.singservice.config.OssConfig
 * @作者 Mr.sandman
 * @时间 2025/05/23 15:15
 */
@Configuration
public class OssConfig {

  @Value("${aliyun.oss.endpoint}")
  private String endpoint;

  @Value("${aliyun.oss.accessKeyId}")
  private String accessKeyId;

  @Value("${aliyun.oss.accessKeySecret}")
  private String accessKeySecret;
  @Bean
  public OSS ossClient() {
    return new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
  }
}
