package com.sing.service.constant;

import com.timevale.tech.sdk.constants.AlgorithmType;

/**
 * SDK3.0请求配置项
 * description 常用配置常量类
 */
public class ConfigConstant {

	//算法类型（可选HMACSHA256/RSA，推荐使用HMACSHA256)
	public static final AlgorithmType ALGORITHM_TYPE = AlgorithmType.HMACSHA256;

	//e签宝公钥，可从开放平台获取，若算法类型为RSA，此项必填
	public static final String ESIGN_PUB_KEY = null;

	//e签宝私钥，可从开放平台下载密钥生成工具生成，若算法为RSA，此项必填
	public static final String ESIGN_PRI_KEY = null;

	// 添加一个pdf文件后缀
	public static final String PDF_SUFFIX = ".pdf";

	// 添加一个/
	public static final String FILE_SEPARATOR = "/";



}
