package com.sing.service.entity;



import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * 合同协议表(SignContract)表实体类
 * <AUTHOR>
 * @since 2025-05-20 14:11:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "SignContract对象")
public class SignContract {

  @TableId(type = IdType.AUTO)
  @Schema(description = "主键id")
  private Long id;

  @Schema(description = "合同类型 1 e签宝相关协议")
  private Integer contractType;

  @Schema(description = "协议名称")
  private String contractName;

  @Schema(description = "流量标识")
  private String trafficCode;

  @Schema(description = "协议地址")
  private String contractUrl;

  @Schema(description = "系统来源 1 资金系统 2 营销系统")
  private String contractSystem;

  @Schema(description = "协议编码")
  private String contractCode;

  @Schema(description = "x坐标")
  private String axesX;

  @Schema(description = "y坐标")
  private String axesY;

  @Schema(description = "签章页数")
  private String axesPage;

  @Schema(description = "1 个人 2 企业 3个人与企业")
  private Integer signType;

  @Schema(description = "企业签章关键字")
  private String keyWord;

  @Schema(description = "签章阶段 1 授信阶段 2 放款阶段")
  private Integer signStage;

  @Schema(description = "签章类型 1 单页签章 2 多页签章")
  private String signPageType;

  @Schema(description = "个人印章签章宽度")
  private Float personWidth;

  @Schema(description = "企业印章签章宽度")
  private Float orgWidth;

  @Schema(description = "创建时间")
  @TableField(fill = FieldFill.INSERT)
  private Date createTime;

  @Schema(description = "创建人id")
  private Long createUserId;

  @Schema(description = "是否删除 0 未删 1 已删")
  private Integer deleted;

}

