package com.sing.service.entity;



import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * {@code @公司} 中数金智(上海)有限公司
 * {@code @包名} com.singservice.sing_service.entity.SignContractLog
 * @作者 Mr.sandman
 * @时间 2025/05/20 14:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "SignContractLog对象")
public class SignContractLog {

  @TableId(type = IdType.AUTO)
  @Schema(description = "主键id")
  private Long id;

  @Schema(description = "合同id")
  private Long contractId;

  @Schema(description = "合同编号")
  private String contractNo;

  @Schema(description = "认证id")
  private String creditId;

  @Schema(description = "身份证号")
  private String cardNo;

  @Schema(description = "协议编码")
  private String contractCode;

  @Schema(description = "流量标识")
  private String trafficCode;

  @Schema(description = "协议名称")
  private String contractName;

  @Schema(description = "协议地址")
  private String contractUrl;

  @Schema(description = "创建时间")
  @TableField(fill = FieldFill.INSERT)
  private Date createTime;

  @Schema(description = "是否删除 0 未删 1 已删")
  private Integer deleted;

}
