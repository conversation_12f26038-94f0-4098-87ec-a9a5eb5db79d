package com.sing.service.entity.dto;


import com.sing.service.entity.vo.AuthVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.singservice.sing_service.entity.dto.EsignDTO
 * @作者 Mr.sandman
 * @时间 2025/05/21 19:02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "e签宝参数")
public class ESignDTO {

    @Schema(description = "协议编码")
    private String contractCode;

    @Schema(description = "流量标识 ")
    private String trafficCode;

    @Schema(description = "授信人id", required = true)
    private String creditId;

    @Schema(description = "联系地址")
    private String address;

    @Schema(description = "手机号", required = true)
    private String phone;

    @Schema(description = "分期次数")
    private String stagesNumber;

    @Schema(description = "借款金额")
    private String amount;

    @Schema(description = "借款合同编号")
    private String amountNo;

    @Schema(description = "个人扣款授权编号")
    private String authNo;

    @Schema(description = "借记卡账户名称")
    private String acctName;

    @Schema(description = "借记卡银行卡号")
    private String bankCardNo;

    @Schema(description = "借记卡开户行")
    private String bankName;

    @Schema(description = "借款用途")
    private String purpose;

    @Schema(description = "大写人民币")
    private String capitalRmb;

    @Schema(description = "小写人民币")
    private String lowercaseRmb;

    @Schema(description = "合同起始年份")
    private String loanActvYear;

    @Schema(description = "合同起始月份")
    private String loanActvMonth;

    @Schema(description = "合同起始日")
    private String loanActvDay;

    @Schema(description = "合同结束年份")
    private String dueYear;

    @Schema(description = "合同结束月份")
    private String dueMonth;

    @Schema(description = "合同结束日")
    private String dueDay;

    @Schema(description = "年化利率")
    private String intRate;

    @Schema(description = "市场定价率(LPR)")
    private String priceRate;

    @Schema(description = "还款方式")
    private String mtdCde;

    @Schema(description = "还款日")
    private String repayDate;

    @Schema(description = "每期还款金额")
    private String repayAmount;

    @Schema(description = "罚息日利率")
    private String guarOdIntRate;

    @Schema(description = "咨询服务费")
    private String serviceFee;

    // 认证信息
    @Schema(description = "认证信息")
    private AuthVo authDto;

}
