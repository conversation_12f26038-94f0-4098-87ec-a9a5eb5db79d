package com.sing.service.entity.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 个人认证证据点认证信息
 * @公司 中数金智(上海)有限公司
 * @包名 com.singservice.sing_service.entity.vo.EviVo
 * @作者 Mr.sandman
 * @时间 2025/05/21 11:39
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "个人认证证据点认证信息")
public class AuthVo {

  @Schema(description = "证据点名称")
  private String eviName;

  @Schema(description = "认证方式 FACE-人脸认证")
  private String realNameMethod;

  @Schema(description = "认证完成时间（毫秒级时间戳）")
  private Long timeStamp;

  @Schema(description = "个人姓名")
  private String name;

  @Schema(description = "个人证件号")
  private String cardNo;

  @Schema(description = "证件类型 CRED_PSN_CH_IDCARD - 中国大陆居民身份证")
  private String cardType;

  @Schema(description = "身份证正面图片ossKey")
  private String cardPortrait;

  @Schema(description = "身份证反面图片ossKey")
  private String cardNationalEmblem;

  @Schema(description = "人脸认证信息")
  private FaceVo face;

}
