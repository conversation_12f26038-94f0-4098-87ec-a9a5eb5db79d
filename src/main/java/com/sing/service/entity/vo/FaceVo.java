package com.sing.service.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.singservice.sing_service.entity.vo.FaceVo
 * @作者 Mr.sandman
 * @时间 2025/05/21 15:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "人脸认证信息")
public class FaceVo {

  @Schema(description = "信息核验供应商")
  private String infoVerifySupplier;

  @Schema(description = "核验时间")
  private String verifyTime;

  @Schema(description = "核验流水号")
  private String verifySerialNumber;

  @Schema(description = "人脸照片文件ossKey")
  private String faceImage;

  @Schema(description = "人脸照片相似度得分")
  private String faceImageSimilarScore;

  @Schema(description = "人脸活体检测得分")
  private String faceLiveDetectScore;

}
