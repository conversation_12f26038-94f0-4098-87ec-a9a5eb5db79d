package com.sing.service.entity.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 个人实名证据点文件上传实体类
 * @公司 中数金智(上海)有限公司
 * @包名 com.singservice.sing_service.entity.vo.FileVo
 * @作者 Mr.sandman
 * @时间 2025/05/21 11:47
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class FileVo {

  @Schema(description = "文件内容MD5值")
  private String contentMd5;

  @Schema(description = "文件类型 application/octet-stream application/pdf")
  private String contentType;

  @Schema(description = "文件名称 必须带上文件扩展名")
  private String fileName;

  @Schema(description = "文件大小")
  private int fileSize;

}
