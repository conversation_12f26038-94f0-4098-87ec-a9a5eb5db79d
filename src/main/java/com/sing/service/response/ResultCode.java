package com.sing.service.response;

import lombok.Getter;
import lombok.Setter;

/**
 * describe:
 *
 * <AUTHOR>
 * @date 2019/12/07
 */

public enum ResultCode {

    SUCCESS("200", "操作成功"),
    CONTRACT_NOT_EXIST("1001","合同编号不能为空"),
    UNAUTHORIZED("401","验签失败"),
    DATA_REPEAT("206","数据重复"),

    /**
     * 从1001序列号开始，依次记录异常信息
     */
    SHOP_ID_NOT_NULL("1001","shopId不能为空"),
    TILL_ID_NOT_NULL("1002","tillId不能为空"),
    CLOSE_TYPE_NOT_NULL("1003","closeType不能为空");
    ;


    @Getter
    @Setter
    private String code;

    @Getter
    @Setter
    private String message;


    ResultCode( String code, String message ) {
        this.code = code;
        this.message = message;
    }


}
