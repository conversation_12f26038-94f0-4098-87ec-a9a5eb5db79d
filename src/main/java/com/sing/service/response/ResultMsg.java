package com.sing.service.response;


import lombok.Getter;
import lombok.Setter;

/**
 * describe:
 *
 * <AUTHOR>
 * @date 2019/12/07
 */
@Getter
@Setter
public class ResultMsg {


    private String code;

    private String message;

    private Object object;

    public void setResult(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public void setResult(String code, String message,
                          Object object) {
        this.code = code;
        this.message = message;
        this.object = object;
    }


    public ResultMsg( String code, String message ) {
        this.code = code;
        this.message = message;
    }

    public ResultMsg( String code, String message, Object object ) {
        this.code = code;
        this.message = message;
        this.object = object;
    }

    public ResultMsg() {

    }
}
