package com.sing.service.service;



import com.sing.service.entity.SignContract;
import com.sing.service.entity.dto.ESignDTO;
import com.sing.service.error.EsignDemoException;
import com.sing.service.response.ResultMsg;
import org.springframework.web.multipart.MultipartFile;

/**
 * 签章服务
 * @公司 中数金智(上海)有限公司
 * @包名 com.singservice.sing_service.service.EsignService
 * @作者 Mr.sandman
 * @时间 2025/05/22 14:19
 */
public interface ESignService {

  /**
   * 签章
   * @param eSignDTO 签章参数
   * @return ResultMsg 签章后的结果
   */
  ResultMsg sign( ESignDTO eSignDTO ) throws EsignDemoException;

  /**
   * 获取签章链接
   * @param contractNo 签章合同编号
   * @return ResultMsg 签章链接
   */
  ResultMsg getSignUrl(String contractNo);

  /**
   * 根据关键字获取签章位置
   */
  ResultMsg getDocKeyPosition( MultipartFile file, String keyWords ) throws EsignDemoException;

  /**
   * 上传文件到oss获取
   */
  ResultMsg uploadFileToOss( MultipartFile file) throws EsignDemoException;

  /**
   * 填充pdf文件模版
   */
  ResultMsg createFileFromTemplate( MultipartFile file) throws EsignDemoException;

  /**
   * 获取oss文件url
   */
  ResultMsg getOssUrl(String ossKey) throws EsignDemoException;

  /**
   * 保存合同表内容
   */
  ResultMsg saveContract( SignContract contractDTO );

}
