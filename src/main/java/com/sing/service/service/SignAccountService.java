package com.sing.service.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.sing.service.entity.SignAccount;
import com.sing.service.entity.SignContract;
import com.sing.service.entity.vo.AuthVo;
import com.sing.service.entity.vo.UploadDataVo;
import com.sing.service.error.EsignDemoException;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * (SignAccount)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-20 18:52:58
 */
public interface SignAccountService extends IService<SignAccount> {

  /**
   * 根据关键字获取坐标位置
   * @param filePath 文件路径
   * @param keyWords 关键字
   * 注意：
   * （1）要用英文的逗号，不能用中文逗号；
   * （2）关键字建议不要设置特殊字符，某些特殊字符可能会因解析失败导致搜索不到；
   * （3）扫描或图片生成的PDF文件无法检索关键字。
   * @return 坐标信息
   */
  List getDocKeyPosition( String filePath, String keyWords ) throws EsignDemoException;

  /**
   * 获取上传文件地址
   * @param filePath 文件路径
   * @return 文件上传地址
   */
  UploadDataVo getUploadUrl( String filePath ) throws EsignDemoException;

  /**
   * 获取文件状态
   * @param fileId 文件id
   * @return 文件状态
   */
  String getFileStatus(String fileId) throws EsignDemoException;

  /**
   * 文件流上传
   * @param uploadUrl getUploadUrl方法得到的地址
   * @param filePath 文件路径
   */
  boolean uploadFileStrem(String uploadUrl,String filePath) throws EsignDemoException;

  /**
   * 创建个人实名证据点
   * @param authVo 认证参数
   * @return 认证结果
   */
  String eviPoint( AuthVo authVo ) throws EsignDemoException;

  /**
   * 创建个人签署账号
   */
  String addPersonAccount(String name, String idNo, String eviPointId) throws EsignDemoException;

  /**
   * 创建个人模版印章
   */
  String createPersonalSeal(String accountId) throws EsignDemoException;

  /**
   * 个人用户PDF文件签署
   */
  byte[] personSign( SignAccount signAccount, SignContract signContract, String fileName, byte[] pdfBytes ) throws EsignDemoException;

  /**
   * 平台自身PDF文件签署
   */
  byte[] platformSign(SignAccount signAccount, SignContract  signContract, String fileName, byte[] pdfBytes) throws EsignDemoException;

  /**
   * 本地PDF文件验签
   */
  boolean localVerifyPdf(String srcPdfFile) throws EsignDemoException;

  /**
   * 填充本地pdf模版文件
   */
  byte[] createFileFromTemplate(byte[] fileBytes, String fileName, Map<String, Object> txtFields ) throws EsignDemoException;

  /**
   * 根据osskey获取下载链接
   */
  String getOssUrl(String ossKey) throws EsignDemoException;

  /**
   * 上传文件到oss获取
   */
  String uploadFile( InputStream inputStream, long contentLength, boolean idPdf) throws EsignDemoException;
}

