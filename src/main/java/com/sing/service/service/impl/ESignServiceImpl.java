package com.sing.service.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.sing.service.entity.SignAccount;
import com.sing.service.entity.SignContract;
import com.sing.service.entity.SignContractLog;
import com.sing.service.entity.dto.ESignDTO;
import com.sing.service.entity.vo.UploadDataVo;
import com.sing.service.error.EsignDemoException;
import com.sing.service.response.ResultCode;
import com.sing.service.response.ResultMsg;
import com.sing.service.service.ESignService;
import com.sing.service.service.SignAccountService;
import com.sing.service.service.SignContractService;
import com.sing.service.service.SigncontractLogService;
import com.sing.service.util.FileUtils;
import com.sing.service.util.UUIDUtils;

import com.sing.service.util.enums.TrafficSignalEnum;
import org.redisson.api.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.singservice.sing_service.service.impl.EsignServiceImpl
 * @作者 Mr.sandman
 * @时间 2025/05/22 14:20
 */
@Service
public class ESignServiceImpl implements ESignService {

  private static final Logger LOGGER = LoggerFactory.getLogger(ESignServiceImpl.class);

  @Autowired
  private SignAccountService signAccountService;
  @Autowired
  private SignContractService signContractService;
  @Autowired
  private SigncontractLogService signcontractLogService;
  @Autowired
  private TransactionTemplate transactionTemplate;
  @Autowired
  private RedissonClient redissonClient;

  // Redis键配置
  @Value("${esign.redis.queue.key:esign:request:queue}")
  private String esignQueueKey;

  @Value("${esign.redis.processing.key.prefix:esign:processing:}")
  private String esignProcessingKeyPrefix;

  @Value("${esign.redis.rate.limit.key.prefix:esign:rate:limit:}")
  private String rateLimitKeyPrefix;

  @Value("${esign.lock.card.key:esign:lock:card:}")
  private String lockCardKey;

  // 超时配置
  @Value("${esign.timeout.queue:30000}")
  private long queueTimeout;

  @Value("${esign.timeout.processing:60000}")
  private long processingTimeout;

  // 限流配置
  @Value("${esign.rate.limit:50}")
  private int rateLimit;

  @Value("${esign.queue.max.size:1000}")
  private int maxQueueSize;


  /**
   * 签章
   * @param eSignDTO 签章参数
   * @return ResultMsg 签章后的结果
   */
  @Override
  public ResultMsg sign(ESignDTO eSignDTO) throws EsignDemoException {
    String cardNo = eSignDTO.getAuthDto().getCardNo();
    String contractCode = eSignDTO.getContractCode();
    String taskId = UUID.randomUUID().toString();
    long startTime = System.currentTimeMillis();

    LOGGER.info("开始处理签章请求 taskId={}, cardNo={}, contractCode={}",
                taskId, cardNo, contractCode);

    // // 1. 全局限流检查
    // if (!tryAcquireRateLimit()) {
    //   LOGGER.warn("请求被限流拒绝 taskId={}", taskId);
    //   throw new EsignDemoException("系统繁忙，请稍后再试");
    // }

    // 2. 获取身份证号专属锁
    RLock cardLock = redissonClient.getLock(lockCardKey + cardNo);
    // 打印锁的日志
    LOGGER.info("[Lock] 创建锁对象, lockKey={}, thread={}", lockCardKey + cardNo, Thread.currentThread().getName());
    try {
      // 尝试获取锁，等待5秒，锁持有5分钟
      if (!cardLock.tryLock(5, 300, TimeUnit.SECONDS)) {
        LOGGER.warn("获取身份证锁失败 taskId={}", taskId);
        throw new EsignDemoException("系统繁忙，请稍后再试");
      }

      LOGGER.info("[Lock] 获取锁成功, lockKey={}, thread={}, holdTime=30s", lockCardKey + cardNo, Thread.currentThread().getName());

      // 3. 检查是否已有处理中的任务
      String processingKey = esignProcessingKeyPrefix + cardNo;
      RBucket<String> processingBucket = redissonClient.getBucket(processingKey);
      if (processingBucket.get() != null) {
        LOGGER.info("已有处理中的任务，加入队列 taskId={}", taskId);
        return processViaQueue(eSignDTO, taskId, startTime);
      }

      // 4. 标记为处理中
      processingBucket.set("processing", processingTimeout, TimeUnit.MILLISECONDS);

      // 5. 执行签章逻辑
      return executeSignLogic(eSignDTO);

    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      LOGGER.error("签章处理被中断 taskId={}", taskId, e);
      throw new EsignDemoException("处理被中断");
    } finally {
      if (cardLock.isHeldByCurrentThread()) {
        LOGGER.info("[Lock] 释放锁, lockKey={}, thread={}", lockCardKey + cardNo, Thread.currentThread().getName());
        cardLock.unlock();
      }
    }
  }

  /**
   * 通过队列处理请求
   */
  private ResultMsg processViaQueue(ESignDTO eSignDTO, String taskId, long startTime)
      throws EsignDemoException {

    RBlockingQueue<String> queue = redissonClient.getBlockingQueue(esignQueueKey);
    RDelayedQueue<String> delayedQueue = redissonClient.getDelayedQueue(queue);

    try {
      // 1. 检查队列长度
      if (queue.size() >= maxQueueSize) {
        LOGGER.warn("队列已满 taskId={}", taskId);
        throw new EsignDemoException("系统繁忙，请稍后再试");
      }

      // 2. 加入延迟队列
      delayedQueue.offer(taskId, 500, TimeUnit.MILLISECONDS);
      LOGGER.info("任务加入队列 taskId={}, 当前队列大小={}", taskId, queue.size());

      // 3. 等待处理
      while (true) {
        // 检查超时
        if (System.currentTimeMillis() - startTime > queueTimeout) {
          LOGGER.warn("排队超时 taskId={}", taskId);
          throw new EsignDemoException("排队超时，请稍后重试");
        }

        // 检查是否轮到当前任务
        String headTask = queue.peek();
        if (taskId.equals(headTask)) {
          // 获取处理权
          String processingKey = esignProcessingKeyPrefix + eSignDTO.getAuthDto().getCardNo();
          try {
            // 标记为处理中
            redissonClient.getBucket(processingKey)
                .set("processing", processingTimeout, TimeUnit.MILLISECONDS);

            // 从队列移除
            queue.poll();

            // 执行签章逻辑
            return executeSignLogic(eSignDTO);
          } finally {
            redissonClient.getBucket(processingKey).delete();
          }
        }

        // 短暂休眠
        Thread.sleep(200);
      }
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      LOGGER.error("队列处理被中断 taskId={}", taskId, e);
      throw new EsignDemoException("处理被中断");
    } finally {
      delayedQueue.destroy();
    }
  }

  /**
   * 执行签章业务逻辑
   */
  private ResultMsg executeSignLogic(ESignDTO eSignDTO) throws EsignDemoException {
    // 再次检查限流
    if (!tryAcquireRateLimit()) {
      LOGGER.warn("E签宝API调用被限流阻断");
      throw new EsignDemoException("系统繁忙，请稍后再试");
    }

    return transactionTemplate.execute(status -> {
      try {
        // 实际业务逻辑
        return doSignLogic(eSignDTO);
      } catch (EsignDemoException e) {
        status.setRollbackOnly();
        throw new RuntimeException(e);
      }
    });
  }

  /**
   * 获取限流许可
   */
  private boolean tryAcquireRateLimit() {
    RRateLimiter rateLimiter = redissonClient.getRateLimiter(rateLimitKeyPrefix);
    // 初始化限流器 (只在第一次调用时执行)
    rateLimiter.trySetRate(RateType.OVERALL, rateLimit, 1, RateIntervalUnit.SECONDS);
    return rateLimiter.tryAcquire();
  }


  // 以下方法不变
  private ResultMsg doSignLogic(ESignDTO eSignDTO) throws EsignDemoException {
    LOGGER.info("开始处理签章逻辑，请求参数:{}", JSONUtil.toJsonStr(eSignDTO));
    ResultMsg resultMsg = new ResultMsg();

    SignAccount signAccount = signAccountService.getOne(new QueryWrapper<SignAccount>()
                                                            .eq("card_no", eSignDTO.getAuthDto().getCardNo()),false);
    LOGGER.info("查询到的个人账号信息:{}", JSONUtil.toJsonStr(signAccount));
    if (signAccount == null) {
      // 身份证照片地址
      String portraitUrl = signAccountService.getOssUrl(eSignDTO.getAuthDto().getCardPortrait());
      String backUrl = signAccountService.getOssUrl(eSignDTO.getAuthDto().getCardNationalEmblem());
      String faceUrl = signAccountService.getOssUrl(eSignDTO.getAuthDto().getFace().getFaceImage());

      File portraitFile = null, backFile = null, faceFile = null;

      try {
        portraitFile = FileUtils.downloadToLocal(portraitUrl);
        backFile = FileUtils.downloadToLocal(backUrl);
        faceFile = FileUtils.downloadToLocal(faceUrl);

        UploadDataVo portraitUpload = signAccountService.getUploadUrl(portraitFile.getAbsolutePath());
        signAccountService.uploadFileStrem(portraitUpload.getUploadUrl(), portraitFile.getAbsolutePath());
        LOGGER.info("上传身份证正面照状态成功");

        UploadDataVo backUpload = signAccountService.getUploadUrl(backFile.getAbsolutePath());
        signAccountService.uploadFileStrem(backUpload.getUploadUrl(), backFile.getAbsolutePath());
        LOGGER.info("上传身份证反面照状态成功");

        UploadDataVo faceUpload = signAccountService.getUploadUrl(faceFile.getAbsolutePath());
        signAccountService.uploadFileStrem(faceUpload.getUploadUrl(), faceFile.getAbsolutePath());
        LOGGER.info("上传身份证人脸照状态成功");


        String eviPointId = signAccountService.eviPoint(eSignDTO.getAuthDto());
        LOGGER.info("证据点ID: {}", eviPointId);

        SignAccount newAccount = new SignAccount();
        newAccount.setCardNo(eSignDTO.getAuthDto().getCardNo());
        newAccount.setEviPointId(eviPointId);
        newAccount.setCreditId(eSignDTO.getCreditId());
        signAccountService.save(newAccount);

        String accountId = signAccountService.addPersonAccount(
            eSignDTO.getAuthDto().getName(), eSignDTO.getAuthDto().getCardNo(), eviPointId);
        newAccount.setAccountId(accountId);
        signAccountService.updateById(newAccount);

        String sealData = signAccountService.createPersonalSeal(accountId);
        newAccount.setSealData(sealData);
        signAccountService.updateById(newAccount);

        signAccount = newAccount; // 后续继续用这个账户
      } catch (IOException e) {
        throw new EsignDemoException("下载或上传文件失败: " + e.getMessage());
      } finally {
        FileUtils.safeDeleteFile(portraitFile);
        FileUtils.safeDeleteFile(backFile);
        FileUtils.safeDeleteFile(faceFile);
      }
    }

    // 合同签署逻辑，加 try-catch
    String contractNo = UUIDUtils.generateContractUUID();
    byte[] signedPdf;

    try {
      signedPdf = signContract(signAccount, eSignDTO, contractNo);
    } catch (Exception e) {
      LOGGER.error("调用签章服务失败: {}", e.getMessage(), e);
      throw new EsignDemoException("签署合同失败，请稍后重试");
    }

    if (signedPdf != null) {
      String ossKey = signAccountService.uploadFile(new ByteArrayInputStream(signedPdf), signedPdf.length, true);

      SignContract contractObj = signContractService.getOne(new QueryWrapper<SignContract>()
                                                              .eq("contract_code", eSignDTO.getContractCode())
                                                              .eq("traffic_code", eSignDTO.getTrafficCode()));

      SignContractLog contractLog = new SignContractLog();
      contractLog.setContractId(contractObj.getId());
      contractLog.setContractNo(contractNo);
      contractLog.setContractName(contractObj.getContractName());
      contractLog.setContractUrl(ossKey);
      contractLog.setCreateTime(new Date());
      contractLog.setCreditId(eSignDTO.getCreditId());
      contractLog.setContractCode(eSignDTO.getContractCode());
      contractLog.setTrafficCode(eSignDTO.getTrafficCode());
      contractLog.setCardNo(signAccount.getCardNo());
      signcontractLogService.save(contractLog);

      resultMsg.setResult(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), contractNo);
    } else {
      throw new EsignDemoException("合同签章失败：返回PDF为空");
    }

    return resultMsg;
  }


  /**
   * 获取签章链接
   * @param contractNo 签章合同编号
   * @return ResultMsg 签章链接
   */
  @Override
  public ResultMsg getSignUrl( String contractNo ) {
    ResultMsg resultMsg = new ResultMsg();
    if ( StringUtils.isNotBlank(contractNo) )  {
      SignContractLog signContractLog = signcontractLogService.getOne(new LambdaQueryWrapper<SignContractLog>()
                                                                          .eq(SignContractLog :: getContractNo, contractNo));
      if ( signContractLog != null ) {
        String signUrl = signContractLog.getContractUrl();
        if ( StringUtils.isNotBlank(signUrl) ) {
          resultMsg.setResult(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), signUrl);
        }
      }
    } else {
      resultMsg.setResult(ResultCode.CONTRACT_NOT_EXIST.getCode(), ResultCode.CONTRACT_NOT_EXIST.getMessage());
    }
    return resultMsg;
  }

  /**
   * 根据关键字获取签章位置
   * @param keyWords 关键字
   */
  @Override
  public ResultMsg getDocKeyPosition( MultipartFile file, String keyWords ) throws EsignDemoException {
    ResultMsg resultMsg = new ResultMsg();
    File tempFile = null;
    try {
      tempFile = File.createTempFile("upload-", ".pdf");
      file.transferTo(tempFile);
      String filePath = tempFile.getAbsolutePath();
      List docKeyPosition = signAccountService.getDocKeyPosition(filePath, keyWords);
      resultMsg.setResult(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), docKeyPosition);
    } catch (IOException e) {
      throw new EsignDemoException("文件处理失败");
    } finally {
      if (tempFile != null && tempFile.exists()) {
        tempFile.delete();
      }
    }
    return resultMsg;
  }

  /**
   * 上传文件到oss获取
   * @param file 文件
   */
  @Override
  public ResultMsg uploadFileToOss( MultipartFile file ) throws EsignDemoException {
    ResultMsg resultMsg = new ResultMsg();
    try {
      String ossKey;
      // 判断是否是 PDF 文件
      if (file.getOriginalFilename().toLowerCase().endsWith(".pdf")) {
         ossKey = signAccountService.uploadFile(file.getInputStream(), file.getSize(),  true);
      } else {
         ossKey = signAccountService.uploadFile(file.getInputStream(), file.getSize(),  false);
      }
      resultMsg.setResult(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), ossKey);
    } catch (IOException e) {
      throw new EsignDemoException("文件处理失败");
    }
    return resultMsg;
  }

  /**
   * 填充pdf文件模版
   * @param file 文件
   */
  /**
   * 填充pdf文件模版并保存到本地路径
   * @param file 上传的PDF模板文件
   */
  @Override
  public ResultMsg createFileFromTemplate(MultipartFile file) throws EsignDemoException {
    ResultMsg resultMsg = new ResultMsg();

    try {
      // 获取PDF填充信息
      Map<String, Object> pdfFillInfo = getPdfFillInfoTest();

      // 读取上传文件的字节流
      byte[] templateBytes = file.getBytes();

      // 调用模板填充方法生成新的PDF字节流
      byte[] filledPdfBytes = signAccountService.createFileFromTemplate(templateBytes, file.getOriginalFilename(), pdfFillInfo);

      // 定义本地保存路径（例如：/data/pdfs/）
      String localSavePath = "/Users/<USER>/Downloads/data/";
      File saveDir = new File(localSavePath);
      if (!saveDir.exists()) {
        saveDir.mkdirs(); // 创建目录
      }

      // 生成唯一文件名，防止覆盖
      String outputFileName = "filled_" + System.currentTimeMillis() + ".pdf";
      File outputFile = new File(saveDir, outputFileName);

      // 写入本地文件
      try (FileOutputStream fos = new FileOutputStream(outputFile)) {
        fos.write(filledPdfBytes);
      }

      // 返回本地存储路径或文件名
      resultMsg.setResult(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), outputFileName);

    } catch (IOException e) {
      throw new EsignDemoException("文件处理失败: " + e.getMessage());
    }

    return resultMsg;
  }

  /**
   * 获取oss文件url
   *
   * @param ossKey
   */
  @Override
  public ResultMsg getOssUrl( String ossKey ) throws EsignDemoException {
    ResultMsg resultMsg = new ResultMsg();
    String ossUrl = signAccountService.getOssUrl(ossKey);
    resultMsg.setResult(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), ossUrl);
    return resultMsg;
  }

  /**
   * 保存合同表内容
   *
   * @param contractDTO
   */
  @Override
  public ResultMsg saveContract( SignContract contractDTO ) {
    signContractService.save(contractDTO);
    return new ResultMsg(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage());
  }


  /**
   * 签名合同 公共方法
   */
  private byte[] signContract(SignAccount signAccount, ESignDTO eSignDTO, String contractNo) throws IOException, EsignDemoException {
    SignContract contractObj= signContractService.getOne(new QueryWrapper<SignContract>()
                                                             .eq("contract_code", eSignDTO.getContractCode())
                                                             .eq("traffic_code", eSignDTO.getTrafficCode()));
    LOGGER.info("合同对象: {}", contractObj);

    String contractUrl = signAccountService.getOssUrl(contractObj.getContractUrl());
    LOGGER.info("合同地址: {}", contractUrl);

    File pdfFile = FileUtils.downloadPdfToLocal(contractUrl);
    LOGGER.info("合同文件: {}", pdfFile);

    try {
      Map<String, Object> pdfFillInfo = getPdfFillInfo(eSignDTO, contractNo);
      byte[] filledPdf = signAccountService.createFileFromTemplate(
          FileUtil.readBytes(pdfFile), pdfFile.getName(), pdfFillInfo);

      byte[] signedPdf = null;
      if (contractObj.getSignType() == 1) {
        signedPdf = signAccountService.personSign(signAccount, contractObj, pdfFile.getName(), filledPdf);
      } else if (contractObj.getSignType() == 2) {
        signedPdf = signAccountService.platformSign(signAccount, contractObj, pdfFile.getName(), filledPdf);
      } else if (contractObj.getSignType() == 3) {
        byte[] personSigned = signAccountService.personSign(signAccount, contractObj, pdfFile.getName(), filledPdf);
        signedPdf = signAccountService.platformSign(signAccount, contractObj, pdfFile.getName(), personSigned);
      }

      return signedPdf;
    } finally {
      FileUtils.safeDeleteFile(pdfFile);
    }
  }



  // 先封装部分pdf填充信息 后续再补充
  protected Map<String, Object> getPdfFillInfo( ESignDTO eSignDTO, String contractNo) {
    //填充信息
    LocalDate currentDate = LocalDate.now();
    Map<String, Object> txtFields = new HashMap<>();// 模板中包含待填充文本域时，文本域Key-Value组合
    txtFields.put("cardNo", eSignDTO.getAuthDto().getCardNo());
    txtFields.put("name", eSignDTO.getAuthDto().getName());
    txtFields.put("address", eSignDTO.getAddress());
    txtFields.put("phone", eSignDTO.getPhone());
    txtFields.put("city", extractCityFromAddress(eSignDTO.getAddress()));
    txtFields.put("amountNo", eSignDTO.getAmountNo());
    txtFields.put("stagesNumber", eSignDTO.getStagesNumber());
    txtFields.put("bankCardNo", eSignDTO.getBankCardNo());
    txtFields.put("bankName", eSignDTO.getBankName());
    txtFields.put("amount", eSignDTO.getAmount());
    txtFields.put("contractNo", contractNo);
    txtFields.put("authNo", eSignDTO.getAuthNo());
    txtFields.put("year", currentDate.getYear());
    txtFields.put("mon", currentDate.getMonth().getValue());
    txtFields.put("day", currentDate.getDayOfMonth());
    txtFields.put("date", currentDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")));
    // 新添加一部分数据
    txtFields.put("purpose", eSignDTO.getPurpose());
    txtFields.put("capitalRmb", eSignDTO.getCapitalRmb());
    txtFields.put("lowercaseRmb", eSignDTO.getLowercaseRmb());
    txtFields.put("loanActvYear", eSignDTO.getLoanActvYear());
    txtFields.put("loanActvMonth", eSignDTO.getLoanActvMonth());
    txtFields.put("loanActvDay", eSignDTO.getLoanActvDay());
    txtFields.put("dueYear", eSignDTO.getDueYear());
    txtFields.put("dueMonth", eSignDTO.getDueMonth());
    txtFields.put("dueDay", eSignDTO.getDueDay());
    txtFields.put("intRate", eSignDTO.getIntRate());
    txtFields.put("priceRate", eSignDTO.getPriceRate());
    txtFields.put("mtdCde", eSignDTO.getMtdCde());
    txtFields.put("repayDate", eSignDTO.getRepayDate());
    txtFields.put("repayAmount", eSignDTO.getRepayAmount());
    txtFields.put("guarOdIntRate", eSignDTO.getGuarOdIntRate());
    txtFields.put("acctName", eSignDTO.getAcctName());
    txtFields.put("serviceFee", eSignDTO.getServiceFee());

    return txtFields;
  }

  // 先封装部分pdf填充信息 测试
  private Map<String, Object> getPdfFillInfoTest() {
    //填充信息
    LocalDate currentDate = LocalDate.now();
    Map<String, Object> txtFields = new HashMap<>();// 模板中包含待填充文本域时，文本域Key-Value组合
    txtFields.put("cardNo", "3422221997023034527");
    txtFields.put("name", "张三");
    txtFields.put("address", "上海市松江区九亭镇河图公园B栋");
    txtFields.put("phone", "*********");
    txtFields.put("city", "西安");
    txtFields.put("amountNo", "123");
    txtFields.put("stagesNumber", "12");
    txtFields.put("amount", "200000");
    txtFields.put("bankCardNo", "*****************");
    txtFields.put("bankName", "上海招商银行松江支行");
    txtFields.put("contractNo",UUIDUtils.generateContractUUID());
    txtFields.put("year", currentDate.getYear());
    txtFields.put("mon", currentDate.getMonth().getValue());
    txtFields.put("day", currentDate.getDayOfMonth());
    txtFields.put("date", currentDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")));
    txtFields.put("purpose", "开公司");
    txtFields.put("capitalRmb", "一万");
    txtFields.put("lowercaseRmb", "10000");
    txtFields.put("loanActvYear", "2023");
    txtFields.put("loanActvMonth", "1");
    txtFields.put("loanActvDay", "1");
    txtFields.put("dueYear", "2025");
    txtFields.put("dueMonth", "12");
    txtFields.put("dueDay", "31");
    txtFields.put("intRate", "12%");
    txtFields.put("priceRate", "30");
    txtFields.put("mtdCde", "等额本息");
    txtFields.put("repayDate", "15");
    txtFields.put("repayAmount", "100");
    txtFields.put("guarOdIntRate", "0.2%");
    txtFields.put("acctName", "招商银行");
    txtFields.put("serviceFee", "100");
    txtFields.put("authNo", "11111");
    return txtFields;
  }

  public static String extractCityFromAddress(String address) {
    if (address == null) return "";

    // 匹配 "省" 后面到 "市" 的内容（包括“市”）
    Pattern pattern = Pattern.compile("省(.*?市)");
    Matcher matcher = pattern.matcher(address);

    if (matcher.find()) {
      return matcher.group(1); // 提取匹配到的城市部分
    }

    // 对于直辖市（如北京市）或没有“省”字样的情况，也尝试直接匹配如 “北京市”
    Pattern directCityPattern = Pattern.compile("^(.*?市)");
    Matcher matcher2 = directCityPattern.matcher(address);
    if (matcher2.find()) {
      return matcher2.group(1);
    }

    return "";
  }

}
