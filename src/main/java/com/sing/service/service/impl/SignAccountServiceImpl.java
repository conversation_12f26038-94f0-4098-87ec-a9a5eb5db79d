package com.sing.service.service.impl;

import cn.hutool.http.HttpRequest;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ctrip.framework.apollo.core.utils.StringUtils;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.sing.service.constant.ConfigConstant;
import com.sing.service.dao.SignAccountMapper;
import com.sing.service.entity.SignAccount;
import com.sing.service.entity.SignContract;
import com.sing.service.entity.vo.AuthVo;
import com.sing.service.entity.vo.FileVo;
import com.sing.service.entity.vo.UploadDataVo;
import com.sing.service.error.EsignDemoException;
import com.sing.service.service.FileService;
import com.sing.service.service.SignAccountService;
import com.sing.service.util.EsignFileUtils;
import com.sing.service.util.EsignHelperUtils;
import com.sing.service.util.UUIDUtils;
import com.sing.service.util.enums.EsignHeaderConstant;
import com.sing.service.util.enums.EsignRequestType;
import com.timevale.esign.paas.tech.bean.bean.PosBean;
import com.timevale.esign.paas.tech.bean.request.*;
import com.timevale.esign.paas.tech.bean.result.*;
import com.timevale.esign.paas.tech.enums.*;
import com.timevale.esign.paas.tech.service.*;
import org.apache.http.entity.ContentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * (SignAccount)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-20 18:52:58
 */
@Service
public class SignAccountServiceImpl extends ServiceImpl<SignAccountMapper, SignAccount> implements SignAccountService {

  private static final Logger LOGGER = LoggerFactory.getLogger(SignAccountServiceImpl.class);

  @Value("${oss.bucket.name}")
  private String bucket;
  // esign服务
  @Value("${esign.project.id}")
  private String projectId;
  @Value("${esign.project.secret}")
  private String projectSecret;
  @Value("${esign.api.host}")
  private String apiHost;
  @Value("${esign.point.host}")
  private String pointHost;
  @Value("${esign.point.url}")
  private String pointUrl;
  @Value("${esign.file.upload.url}")
  private String fileUploadUrl;
  @Value("${esign.file.status.url.start}")
  private String fileStatusUrlStart;
  @Value("${esign.file.status.url.end}")
  private String fileStatusUrlEnd;
  @Value("${esign.api.url}")
  private String apiUrl;
  @Value("${esign.seal.id}")
  private String orgSealId;

  @Value("${sing.service.ossPath}")
  private String singServiceOssPath;

  @Autowired
  private PdfDocumentService pdfDocumentService;
  @Autowired
  private AccountService accountService;
  @Autowired
  private TemplateSealService templateSealService;
  @Autowired
  private UserSignService userSignService;
  @Autowired
  private PlatformSignService platformSignService;
  @Autowired
  private SignVerifyService signVerifyService;
  @Autowired
  private FileService fileService;

  Gson gson = new Gson();

  /**
   * 根据关键字获取坐标位置
   * @param filePath 文件路径
   * @param keyWords 关键字
   *  注意：
   *（1）要用英文的逗号，不能用中文逗号；
   *（2）关键字建议不要设置特殊字符，某些特殊字符可能会因解析失败导致搜索不到；
   *（3）扫描或图片生成的PDF文件无法检索关键字。
   * @return 坐标信息
   */
  @Override
  public List getDocKeyPosition( String filePath, String keyWords ) throws EsignDemoException {
    // // 待获取坐标位置的PDF文件路径
    // filePath ="/user/local/pdf/test.pdf";
    // // 待获取坐标位置的关键字
    // keyWords ="甲方盖章,乙方签名";

    DocKeywordPositionResult result = pdfDocumentService.getDocKeyPosition(filePath, keyWords);
    if (result.success()) {
      LOGGER.info("获取坐标位置成功",  result.getDocKeyWordPositionBeans().toArray());
      return result.getDocKeyWordPositionBeans();
    }
    throw new EsignDemoException("获取坐标位置失败 " + result.getMsg());
  }


  /**
   * 获取上传文件地址
   * @param filePath 文件路径
   * @return 文件上传地址
   */
  @Override
  public UploadDataVo getUploadUrl( String filePath ) throws EsignDemoException {
    //自定义的文件封装类，传入文件地址可以获取文件的名称大小,文件流等数据
    EsignFileUtils esignFileUtils = new EsignFileUtils(filePath);
    FileVo fileVo = new FileVo();
    fileVo.setFileName(esignFileUtils.getFileName());
    fileVo.setFileSize(esignFileUtils.getFileSize());
    fileVo.setContentMd5(esignFileUtils.getFileContentMD5());
    fileVo.setContentType(EsignHeaderConstant.CONTENTTYPE_STREAM.VALUE());
    String jsonParam = gson.toJson(fileVo);
    //生成签名鉴权方式的的header
    Map<String, String> header = EsignHelperUtils.signAndBuildSignAndJsonHeader(
        projectId,
        projectSecret,
        jsonParam,
        EsignRequestType.POST.name(),
        fileUploadUrl,
        true);
    // 发送HTTP POST请求
    String response = HttpRequest.post(pointHost + fileUploadUrl)
        .addHeaders(header)
        .body(jsonParam)
        .execute()
        .body();
    JsonObject jsonObject = gson.fromJson(response, JsonObject.class);
    int code = jsonObject.get("code").getAsInt();
    if ( code != 0 ) {
      throw new EsignDemoException("获取上传文件地址失败" + jsonObject.get("message").getAsString());
    }
    // 解析 data 字段为 UploadDataVo 对象
    return gson.fromJson(jsonObject.get("data"), UploadDataVo.class);
  }

  /**
   * 获取文件状态
   *
   * @param fileId 文件id
   * @return 文件状态
   */
  @Override
  public String getFileStatus(String fileId)  throws EsignDemoException {
    String apiUrl = fileStatusUrlStart + fileId + fileStatusUrlEnd;
    //生成签名鉴权方式的的header
    Map<String, String> header = EsignHelperUtils.signAndBuildSignAndJsonHeader(
        projectId,
        projectSecret,
        null,
        EsignRequestType.GET.name(),
        apiUrl,
        true);
    // 发送HTTP GET请求并添加header
    String response = HttpRequest.get(pointHost + apiUrl)
        .addHeaders(header)
        .execute()
        .body();
    JsonObject jsonObject = gson.fromJson(response, JsonObject.class);
    int code = jsonObject.get("code").getAsInt();
    if ( code != 0 ) {
      throw new EsignDemoException("获取文件状态失败" + jsonObject.get("message").getAsString());
    }
    return jsonObject.getAsJsonObject("data").get("status").getAsString();
  }

  /**
   * 文件流上传
   * @param uploadUrl 上传地址 getUploadUrl方法得到的地址
   * @param filePath  文件路径
   */
  @Override
  public boolean uploadFileStrem( String uploadUrl, String filePath ) throws EsignDemoException {
    //自定义的文件封装类，传入文件地址可以获取文件的名称大小,文件流等数据
    EsignFileUtils esignFileUtils = new EsignFileUtils(filePath);
    Map<String, String> header = EsignHelperUtils.buildUploadHeader(esignFileUtils.getFileContentMD5(),
                                                                             EsignHeaderConstant.CONTENTTYPE_STREAM.VALUE());
    // 读取文件二进制内容作为 body（或根据接口要求构造 JSON body）
    byte[] fileBytes = esignFileUtils.getFileBytes(); // 你需要实现这个方法读取文件字节

    // 发送 HTTP PUT 请求，并添加 header 和 body
    String response = HttpRequest.put(uploadUrl)
        .addHeaders(header)
        .body(fileBytes)
        .execute()
        .body();
    JsonObject jsonObject = gson.fromJson(response, JsonObject.class);
    int code = jsonObject.get("errCode").getAsInt();
    if ( code != 0 ) {
      throw new EsignDemoException("文件流上传失败：" + jsonObject.get("msg").getAsString());
    }
    return true;
  }

  /**
   * 创建个人实名证据点
   * @param authVo 认证参数
   * @return 认证结果
   */
  @Override
  public String eviPoint( AuthVo authVo ) throws EsignDemoException {
    String jsonParam = gson.toJson(authVo);
    //生成签名鉴权方式的的header
    Map<String, String> header = EsignHelperUtils.signAndBuildSignAndJsonHeader(
        projectId,
        projectSecret,
        jsonParam,
        EsignRequestType.POST.name(),
        pointUrl,
        true);
    // 发送HTTP POST请求
    String response = HttpRequest.post(pointHost + pointUrl)
        .addHeaders(header)
        .body(jsonParam)
        .execute()
        .body();
    JsonObject jsonObject = gson.fromJson(response, JsonObject.class);
    int code = jsonObject.get("code").getAsInt();
    if ( code != 0 ) {
      throw new EsignDemoException("创建个人实名证据点失败" + jsonObject.get("message").getAsString());
    }
    return jsonObject.getAsJsonObject("data").get("eviPointId").getAsString();
  }

  /**
   * 创建个人签署账号
   * @param name 姓名
   * @param idNo 身份证号
   * @param eviPointId 实名证据点ID
   */
  @Override
  public String addPersonAccount( String name, String idNo, String eviPointId ) throws EsignDemoException {
    LOGGER.info("创建个人签署账号参数 {},{},{}", name, idNo, eviPointId);
    PersonParam personParam = new PersonParam();
    personParam.setName(name);
    personParam.setIdNo(idNo);
    personParam.setIdNoType(IdNoTypeEnum.MAINLAND);
    personParam.setRealnameEvidencePointId(eviPointId);
    AddAccountResult addAccountResult = accountService.addAccount(personParam);
    if ( addAccountResult.getErrCode() != 0 ) {
      throw new EsignDemoException("创建个人签署账号失败" + addAccountResult.getMsg());
    }
    return addAccountResult.getAccountId();
  }

  /**
   * 创建个人模版印章
   */
  @Override
  public String createPersonalSeal( String accountId) throws EsignDemoException {
    LOGGER.info("创建个人模版印章参数 {}", accountId);
    PersonTemplateType type = PersonTemplateType.RECTANGLE; // 印章模板类型,可选：SQUARE-正方形印章 | RECTANGLE-矩形印章 | BORDERLESS-无框矩形印章
    SealColor color = SealColor.RED;// 印章颜色：RED-红色 | BLUE-蓝色 | BLACK-黑色
    StampRuleEnum stampRule = StampRuleEnum.SEAL_NONE;//印章是否加“印”规则：SEAL_NONE，无‘印’|  SEAL_ONE，带‘印’|  SEAL_TWO，带‘之印’
    AddSealResult psnSeal = templateSealService.createPsnSeal(accountId, "", type, color, stampRule);
    if ( psnSeal.getErrCode() != 0 ) {
      throw new EsignDemoException("创建个人模版印章失败" + psnSeal.getMsg());
    }
    return psnSeal.getSealData();
  }

  /**
   * 个人用户PDF文件签署
   *
   */
  @Override
  public byte[] personSign( SignAccount signAccount, SignContract signContract, String fileName, byte[] pdfBytes ) throws EsignDemoException {
    /*
      个人用户PDF文件签署
     */
    // 签署文件信息
    SignFilePdfParam file = new SignFilePdfParam();
    file.setFileName(fileName);// 文件名称
    file.setStreamFile(pdfBytes);

    // 签章位置信息
    List<PosBean> posBeans = new ArrayList<>();
    // 签章类型 2 多页签章 1 单页签章
    if (signContract.getSignPageType().equals("2")) {
      // 获取多个x坐标
      String[] Xs = signContract.getAxesX().split(",");
      // 获取多个y坐标
      String[] Ys = signContract.getAxesY().split(",");

      String[] pages = signContract.getAxesPage().split(",");

      // 确保x和y坐标的数量一致
      if (Xs.length == Ys.length) {
        for (int i = 0; i < Xs.length; i++) {
          PosBean signPos = new PosBean();
          signPos.setPosPage(pages[i]);// 签署页码（除关键字签章外，页码均不可为空）若为多页签章，支持指定多个页码，例如格式：“1-3,5,8“
          signPos.setPosX(Float.parseFloat(Xs[i])); // 添加单个x坐标
          signPos.setPosY(Float.parseFloat(Ys[i])); // 添加单个y坐标
          signPos.setWidth(signContract.getPersonWidth());// 印章展现宽度，将以此宽度对印章图片做同比缩放。
          signPos.setAddSignTime(false);// 是否显示本地签署时间，需要width设置92以上才可以看到时间
          posBeans.add(signPos); // 将新的PosBean添加到列表中
        }
      } else {
        // 处理x和y坐标数量不一致的情况
        throw new EsignDemoException("X和Y坐标的数量必须一致");
      }
    } else if (signContract.getSignPageType().equals("1")) {
      PosBean signPos = new PosBean();
      signPos.setPosPage(signContract.getAxesPage());// 签署页码（除关键字签章外，页码均不可为空）若为多页签章，支持指定多个页码，例如格式：“1-3,5,8“
      signPos.setPosX(Float.parseFloat(signContract.getAxesX()));// 签署位置X坐标，若为关键字定位，相对于关键字的X坐标偏移量，默认0
      signPos.setPosY(Float.parseFloat(signContract.getAxesY()));// 签署位置Y坐标，若为关键字定位，相对于关键字的Y坐标偏移量，默认0
      // signPos.setKeyWord("乙方签名");// 关键字，仅限关键字签章时有效，若为关键字定位时，不可空
      signPos.setWidth(signContract.getPersonWidth());// 印章展现宽度，将以此宽度对印章图片做同比缩放。
      signPos.setAddSignTime(false);// 是否显示本地签署时间，需要width设置92以上才可以看到时间
      posBeans.add(signPos);
    }


    //个人用户印章Base64数据，需要和个人账号对应的个人名字一致
    String sealData = signAccount.getSealData();
    //传入个人用户签署参数内
    PersonSignParam personSignParam = new PersonSignParam();
    personSignParam.setAccountId(signAccount.getAccountId());//个人账号ID（创建个人签署账号接口返回）
    personSignParam.setSealData(sealData);//印章Base64数据
    personSignParam.setPosBeans(posBeans);//签章位置信息
    if ( Objects.equals(signContract.getSignPageType(), "1") ) {
      personSignParam.setSignType(SignType.Single);//签章类型：Single，单页签章； Multi，多页签章； Edges，签骑缝章； Key，关键字签章
    } else if ( Objects.equals(signContract.getSignPageType(), "2") ) {
      personSignParam.setSignType(SignType.Multi);//签章类型：Single，单页签章； Multi，多页签章； Edges，签骑缝章； Key，关键字签章
    }
    personSignParam.setWillingnessId(signAccount.getEviPointId());
    personSignParam.setFileBean(file);//签署文件信息
    personSignParam.setSealSpec(SealSpecEnum.IMAGE);

    //开始个人签署
    FileDigestSignResult fileDigestSignResult = userSignService.personSign(personSignParam);
    if ( fileDigestSignResult.getErrCode() != 0 ) {
      throw new EsignDemoException("个人签署失败: " + fileDigestSignResult.getMsg());
    }
    return fileDigestSignResult.getStream();

  }

  /**
   * 平台自身PDF文件签署
   */
  @Override
  public byte[] platformSign( SignAccount signAccount, SignContract signContract, String fileName, byte[] pdfBytes ) throws EsignDemoException {
    // 签署文件信息
    SignFilePdfParam file = new SignFilePdfParam();
    file.setFileName(fileName);// 文件名称
    file.setStreamFile(pdfBytes);

    // 签章位置信息
    List<PosBean> posBeans = new ArrayList<>();
    PosBean signPos = new PosBean();
    signPos.setKeyWord(signContract.getKeyWord());// 关键字，仅限关键字签章时有效，若为关键字定位时，不可空
    signPos.setWidth(signContract.getOrgWidth());// 印章展现宽度，将以此宽度对印章图片做同比缩放。
    signPos.setAddSignTime(false);// 是否显示本地签署时间，需要width设置92以上才可以看到时间
    posBeans.add(signPos);

    //传入平台自身签署参数内
    PlatformSignParam platformSignParam = new PlatformSignParam();
    platformSignParam.setSealId(orgSealId);//印章ID数据
    platformSignParam.setPosBeans(posBeans);//签章位置信息
    platformSignParam.setSignType(SignType.Key);//签章类型：Single，单页签章； Multi，多页签章； Edges，签骑缝章； Key，关键字签章
    platformSignParam.setFileBean(file);//签署文件信息
    platformSignParam.setSealSpec(SealSpecEnum.IMAGE);

    //开始平台自身签署
    FileDigestSignResult fileDigestSignResult = platformSignService.platformSign(platformSignParam);
    if ( fileDigestSignResult.getErrCode() != 0 ) {
      throw new EsignDemoException("平台签署失败: " + fileDigestSignResult.getMsg());
    }
    return fileDigestSignResult.getStream();
  }

  /**
   * 本地PDF文件验签
   * @param srcPdfFile
   */
  @Override
  public boolean localVerifyPdf( String srcPdfFile ) throws EsignDemoException {
    FilePdfParam filePdfParam = new FilePdfParam();
    filePdfParam.setSrcPdfFile(srcPdfFile);
    VerifyPdfResult verifyPdfResult = signVerifyService.localVerifyPdf(filePdfParam);
    if ( verifyPdfResult.getErrCode() != 0 ) {
      LOGGER.error("PDF文件验签失败, {}", verifyPdfResult.getMsg());
      return false;
    }
    return true;
  }

  /**
   * 填充本地pdf模版文件
   * @param txtFields 填充信息
   */
  @Override
  public byte[] createFileFromTemplate(byte[] fileBytes, String fileName, Map<String, Object> txtFields ) throws EsignDemoException {

    //PDF文件信息
    SignFilePdfParam file = new SignFilePdfParam();
    file.setStreamFile(fileBytes);
    file.setFileName(fileName);// 文件名称，若为空则取文件路径中的名称
    FileCreateFromTemplateResult fileCreateFromTemplateResult = pdfDocumentService.createFileFromTemplate(file, true, txtFields);
    if ( fileCreateFromTemplateResult.getErrCode() != 0 ) {
      throw new EsignDemoException("填充本地PDF模版失败" + fileCreateFromTemplateResult.getMsg());
    }
    return fileCreateFromTemplateResult.getStream();
  }

  /**
   * 根据osskey获取下载链接
   * @param ossKey
   */
  @Override
  public String getOssUrl(String ossKey) throws EsignDemoException {
    String ossUrl = fileService.getOssUrl(bucket, ossKey);
    if ( StringUtils.isBlank(ossUrl) ) {
      throw new EsignDemoException("获取OSS文件失败");
    }
    return ossUrl;
  }

  /**
   * 上传文件到oss获取
   */
  @Override
  public String uploadFile(InputStream inputStream, long contentLength, boolean idPdf) throws EsignDemoException {
    // 获取当前日期作为子目录
    String dateDir = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
    // 构建 ossKey 路径
    String fileName;
    if (idPdf) {
      fileName = UUIDUtils.generateCustomUUID() + ConfigConstant.PDF_SUFFIX;
    } else {
      fileName = UUIDUtils.generateCustomUUID();
    }
    String ossKey = singServiceOssPath + dateDir + ConfigConstant.FILE_SEPARATOR + fileName;
    fileService.uploadOss(bucket, ossKey, inputStream, contentLength);
    return ossKey;
  }

  /**
   * 读取流
   * @param inputStream
   * @return
   * @throws IOException
   */
  public static byte[] readStream(InputStream inputStream) throws IOException {
    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
    byte[] buffer = new byte[1024];
    int len;
    while ((len = inputStream.read(buffer)) > -1) {
      byteArrayOutputStream.write(buffer, 0, len);
    }
    byteArrayOutputStream.flush();
    return byteArrayOutputStream.toByteArray();
  }

}

