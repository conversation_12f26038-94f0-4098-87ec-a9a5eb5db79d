package com.sing.service.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sing.service.dao.SignContractMapper;
import com.sing.service.entity.SignContract;
import com.sing.service.service.SignContractService;
import org.springframework.stereotype.Service;

/**
 * 合同协议表(SignContract)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-20 14:11:04
 */
@Service
public class SignContractServiceImpl extends ServiceImpl<SignContractMapper, SignContract> implements SignContractService {

}

