package com.sing.service.service.impl;



import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sing.service.dao.SignContractLogMapper;
import com.sing.service.entity.SignContractLog;
import com.sing.service.service.SigncontractLogService;
import org.springframework.stereotype.Service;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.singservice.sing_service.service.impl.SigncontractLogServiceImpl
 * @作者 Mr.sandman
 * @时间 2025/05/20 14:29
 */
@Service
public class SigncontractLogServiceImpl extends ServiceImpl<SignContractLogMapper, SignContractLog> implements SigncontractLogService {

}
