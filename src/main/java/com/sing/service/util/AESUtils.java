package com.sing.service.util;

import cn.hutool.crypto.SecureUtil;
import org.springframework.stereotype.Component;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.singservice.sing_service.util.AESUtils
 * @作者 Mr.sandman
 * @时间 2025/05/22 14:34
 */
@Component
public class AESUtils {
    private static final String KEY = "1234567890abcdef";

    /**
     * 加密
     * @param idNo 证件号
     * @return 加密后的证件号
     */
    public static String encrypt(String idNo) {
      return SecureUtil.aes(KEY.getBytes()).encryptHex(idNo);
    }

    /**
     * 解密
     * @param idNo 加密后的证件号
     * @return 解密后的证件号
     */
    public static String decrypt(String idNo) {
      return SecureUtil.aes(KEY.getBytes()).decryptStr(idNo);
    }

}
