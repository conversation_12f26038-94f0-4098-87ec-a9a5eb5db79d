package com.sing.service.util;

import com.sing.service.error.EsignDemoException;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.singservice.sing_service.util.EsignFileUtils
 * @作者 Mr.sandman
 * @时间 2025/05/21 11:12
 */
public class EsignFileUtils {
  //文件名称
  private String fileName;
  //文件大小
  private int fileSize;
  //文件内容MD5
  private String fileContentMD5;
  //文件地址
  private String filePath;


  public EsignFileUtils( String filePath ) throws EsignDemoException {
    this.filePath=filePath;
    this.fileContentMD5 = getFileContentMD5(filePath);
    File file = new File(filePath);
    if (!file.exists()) {
      throw new EsignDemoException("文件不存在");
    }
    this.fileName = file.getName();
    this.fileSize = (int) file.length();
  }

  public String getFileName() {
    return fileName;
  }

  public int getFileSize() {
    return fileSize;
  }

  public String getFileContentMD5() {
    return fileContentMD5;
  }

  /**
   * 传入本地文件地址获取二进制数据
   * @return
   * @throws EsignDemoException
   */
  public byte[] getFileBytes() throws EsignDemoException {
    return fileToBytes(filePath);
  }

  public static String getFileContentMD5(String filePath) throws EsignDemoException {
    // 获取文件MD5的二进制数组（128位）
    byte[] bytes = getFileMD5Bytes128(filePath);
    // 对文件MD5的二进制数组进行base64编码
    return Base64.getEncoder().encodeToString(bytes);
  }
  public static byte[] getFileMD5Bytes128(String filePath) throws EsignDemoException {
    FileInputStream fis = null;
    byte[] md5Bytes = null;
    try {
      File file = new File(filePath);
      fis = new FileInputStream(file);
      MessageDigest md5 = MessageDigest.getInstance("MD5");
      byte[] buffer = new byte[1024];
      int length = -1;
      while ((length = fis.read(buffer, 0, 1024)) != -1) {
        md5.update(buffer, 0, length);
      }
      md5Bytes = md5.digest();
      fis.close();
    } catch ( FileNotFoundException e) {
      EsignDemoException ex = new EsignDemoException("文件找不到", e);
      ex.initCause(e);
      throw ex;
    } catch ( NoSuchAlgorithmException e) {
      EsignDemoException ex = new EsignDemoException("不支持此算法", e);
      ex.initCause(e);
      throw ex;
    } catch ( IOException e) {
      EsignDemoException ex = new EsignDemoException("输入流或输出流异常", e);
      ex.initCause(e);
      throw ex;
    } finally {
      if (fis != null) {
        try {
          fis.close();
        } catch (IOException e) {
          EsignDemoException ex = new EsignDemoException("关闭文件输入流失败", e);
          ex.initCause(e);
          throw ex;
        }
      }
    }
    return md5Bytes;
  }

  public static byte[] fileToBytes(String srcFilePath) throws EsignDemoException {
    return getBytes(srcFilePath);
  }

  public static byte[] getBytes(String filePath) throws EsignDemoException {
    File file = new File(filePath);
    FileInputStream fis = null;
    byte[] buffer = null;
    try {
      fis = new FileInputStream(file);
      buffer = new byte[(int) file.length()];
      fis.read(buffer);
    } catch (Exception e) {
      EsignDemoException ex = new EsignDemoException("获取文件字节流失败", e);
      ex.initCause(e);
      throw ex;
    } finally {
      if (fis != null) {
        try {
          fis.close();
        } catch (IOException e) {
          EsignDemoException ex = new EsignDemoException("关闭文件字节流失败", e);
          ex.initCause(e);
          throw ex;
        }
      }
    }
    return buffer;
  }

}
