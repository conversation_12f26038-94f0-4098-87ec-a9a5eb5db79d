package com.sing.service.util;

import com.sing.service.error.EsignDemoException;
import com.sing.service.util.enums.EsignHeaderConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.MessageFormat;
import java.util.*;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.singservice.sing_service.util.EsignHelperUtils
 * @作者 Mr.sandman
 * @时间 2025/05/21 10:34
 */
public class EsignHelperUtils {

  private static final Logger LOGGER = LoggerFactory.getLogger(EsignHelperUtils.class);

  public static Map<String,String> signAndBuildSignAndJsonHeader(String projectId, String secret,String paramStr,String httpMethod,String url,boolean debug) throws EsignDemoException {
    String contentMD5="";
    //统一转大写处理
    httpMethod = httpMethod.toUpperCase();
    if("GET".equals(httpMethod)||"DELETE".equals(httpMethod)){
      paramStr=null;
      contentMD5="";
    } else if("PUT".equals(httpMethod)||"POST".equals(httpMethod)){
      //对body体做md5摘要
      contentMD5= doContentMD5(paramStr);
    }else{
      throw new EsignDemoException(String.format("不支持的请求方法%s",httpMethod));
    }
    //构造一个初步地请求头
    Map<String, String> esignHeaderMap = buildSignAndJsonHeader(projectId, contentMD5, EsignHeaderConstant.ACCEPT.VALUE(), EsignHeaderConstant.CONTENTTYPE_JSON.VALUE(), EsignHeaderConstant.AUTHMODE.VALUE());
    //排序
    url= sortApiUrl(url);
    //传入生成的bodyMd5,加上其他请求头部信息拼接成字符串
    String message = appendSignDataString(httpMethod, esignHeaderMap.get("Content-MD5"),esignHeaderMap.get("Accept"),esignHeaderMap.get("Content-Type"),esignHeaderMap.get("Headers"),esignHeaderMap.get("Date"), url);
    //整体做sha256签名
    String reqSignature = doSignatureBase64(message, secret);
    //请求头添加签名值
    esignHeaderMap.put("X-Tsign-Open-Ca-Signature",reqSignature);
    if(debug){
      LOGGER.info("----------------------------start------------------------");
      LOGGER.info("待计算body值:{}", paramStr+"\n");
      LOGGER.info("MD5值:{}",contentMD5+"\n");
      LOGGER.info("待签名字符串:{}",message+"\n");
      LOGGER.info("签名值:{}",reqSignature+"\n");
    }
    return esignHeaderMap;
  }

  /**
   * 构建一个签名鉴权+json数据的esign请求头
   * @return 请求头
   * <AUTHOR>
   */
  public static Map<String, String> buildSignAndJsonHeader( String projectId, String contentMD5, String accept, String contentType, String authMode ) {

    Map<String, String> header = new HashMap<>();
    header.put("X-Tsign-Open-App-Id", projectId);
    header.put("X-Tsign-Open-Ca-Timestamp", timeStamp());
    header.put("Accept",accept);
    header.put("Content-MD5",contentMD5);
    header.put("Content-Type", contentType);
    header.put("X-Tsign-Open-Auth-Mode", authMode);
    return header;
  }

  /**
   * 获取时间戳
   */
  public static String timeStamp() {
    long timeStamp = System.currentTimeMillis();
    return String.valueOf(timeStamp);
  }

  /**
   * 获取内容MD5
   */
  public static String doContentMD5(String str) throws EsignDemoException {
    byte[] md5Bytes = null;
    MessageDigest md5 = null;
    String contentMD5 = null;
    try {
      md5 = MessageDigest.getInstance("MD5");
      // 计算md5函数
      md5.update(str.getBytes(StandardCharsets.UTF_8));
      // 获取文件MD5的二进制数组（128位）
      md5Bytes = md5.digest();
      // 把MD5摘要后的二进制数组md5Bytes使用Base64进行编码（而不是对32位的16进制字符串进行编码）
      contentMD5 =  Base64.getEncoder().encodeToString(md5Bytes);

    } catch ( NoSuchAlgorithmException e) {
      EsignDemoException ex = new EsignDemoException("不支持此算法",e);
      ex.initCause(e);
      throw ex;
    }
    return contentMD5;
  }

  /**
   * 排序url
   * @param apiUrl
   * @return
   * @throws EsignDemoException
   */
  public static String sortApiUrl(String apiUrl) throws EsignDemoException {

    if (!apiUrl.contains("?")) {
      return apiUrl;
    }

    int queryIndex = apiUrl.indexOf("?");
    String apiUrlPath =apiUrl.substring(0,queryIndex+1);
    String apiUrlQuery = apiUrl.substring(queryIndex+1);
    //apiUrlQuery为空时返回
    if(isBlank(apiUrlQuery)){
      return apiUrl.substring(0,apiUrl.length()-1);
    }
    // 请求URL中Query参数转成Map
    Map<Object, Object> queryParamsMap = new HashMap<Object, Object>();
    String[] params = apiUrlQuery.split("&");
    for (String str : params) {
      int index = str.indexOf("=");
      String key = str.substring(0, index);
      String value = str.substring(index + 1);
      if (queryParamsMap.containsKey(key)) {
        String msg = MessageFormat.format("请求URL中的Query参数的{0}重复", key);
        throw new EsignDemoException(msg);
      }
      queryParamsMap.put(key, value);
    }

    ArrayList<String> queryMapKeys = new ArrayList<String>();
    for (Map.Entry<Object, Object> entry : queryParamsMap.entrySet()) {
      queryMapKeys.add((String) entry.getKey());
    }
    // 按照字段名的 ASCII 码从小到大排序（字典排序）
    Collections.sort(queryMapKeys, new Comparator<String>() {
      @Override
      public int compare(String o1, String o2) {
        return (o1.compareToIgnoreCase(o2) == 0 ? -o1.compareTo(o2) : o1.compareToIgnoreCase(o2));
      }
    });

    StringBuffer queryString = new StringBuffer();
    // 构造Query参数键值对值对的格式
    for (int i = 0; i < queryMapKeys.size(); i++) {
      String key = queryMapKeys.get(i);
      String value = (String) queryParamsMap.get(key);
      queryString.append(key);
      queryString.append("=");
      queryString.append(value);
      queryString.append("&");
    }
    if (queryString.length() > 0) {
      queryString = queryString.deleteCharAt(queryString.length() - 1);
    }

    // Query参数排序后的接口请求地址
    StringBuffer sortApiUrl = new StringBuffer();
    sortApiUrl.append(apiUrlPath);
    sortApiUrl.append(queryString.toString());
    return sortApiUrl.toString();
  }

  /**
   * 拼接待签名字符串
   * @param httpMethod
   * @param contentMd5
   * @param accept
   * @param contentType
   * @param headers
   * @param date
   * @param url
   * @return
   * @throws EsignDemoException
   */
  public static String appendSignDataString(String httpMethod, String contentMd5,String accept,String contentType,String headers,String date, String url) throws EsignDemoException {
    StringBuffer sb = new StringBuffer();
    sb.append(httpMethod).append("\n").append(accept).append("\n").append(contentMd5).append("\n")
        .append(contentType).append("\n");

    if ("".equals(date) || date == null) {
      sb.append("\n");
    } else {
      sb.append(date).append("\n");
    }
    if ("".equals(headers) || headers == null) {
      sb.append(url);
    } else {
      sb.append(headers).append("\n").append(url);
    }
    return new String(sb);
  }

  public static String doSignatureBase64(String message, String secret) throws EsignDemoException {
    String algorithm = "HmacSHA256";
    Mac hmacSha256;
    String digestBase64 = null;
    try {
      hmacSha256 = Mac.getInstance(algorithm);
      byte[] keyBytes = secret.getBytes("UTF-8");
      byte[] messageBytes = message.getBytes("UTF-8");
      hmacSha256.init(new SecretKeySpec(keyBytes, 0, keyBytes.length, algorithm));
      // 使用HmacSHA256对二进制数据消息Bytes计算摘要
      byte[] digestBytes = hmacSha256.doFinal(messageBytes);
      // 把摘要后的结果digestBytes使用Base64进行编码
      digestBase64 = Base64.getEncoder().encodeToString(digestBytes);
    } catch (NoSuchAlgorithmException e) {
      EsignDemoException ex = new EsignDemoException("不支持此算法",e);
      ex.initCause(e);
      throw ex;
    } catch ( InvalidKeyException e) {
      EsignDemoException ex = new EsignDemoException("无效的密钥规范",e);
      ex.initCause(e);
      throw ex;
    } catch ( UnsupportedEncodingException e) {
      e.printStackTrace();
    }
    return digestBase64;
  }

  /**
   * 创建文件流上传请求头
   */
  public static Map<String, String> buildUploadHeader(String fileContentMd5, String contentType) {
    Map<String, String> header = new HashMap<>();
    header.put("Content-MD5", fileContentMd5);
    header.put("Content-Type", contentType);
    return header;
  }


  public static boolean isBlank(String str) {
    if (null == str || 0 == str.length()) {
      return true;
    }

    int strLen = str.length();

    for (int i = 0; i < strLen; i++) {
      if (!Character.isWhitespace(str.charAt(i))) {
        return false;
      }
    }
    return true;
  }

}
