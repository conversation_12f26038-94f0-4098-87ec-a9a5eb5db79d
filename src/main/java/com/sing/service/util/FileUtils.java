package com.sing.service.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.singservice.sing_service.util.FileUtils
 * @作者 Mr.sandman
 * @时间 2025/05/22 16:25
 */
public class FileUtils {

  private static final Logger LOGGER = LoggerFactory.getLogger(FileUtils.class);

  /**
   * 将远程 URL 的文件下载为本地临时文件 jpg
   */
  public static File downloadToLocal(String fileUrl) throws IOException {
    URL url = new URL(fileUrl);
    File tempFile = File.createTempFile("esign-", ".jpg"); // 创建临时 JPG 文件
    tempFile.deleteOnExit(); // JVM 关闭时兜底删除

    try ( InputStream in = url.openStream();
          FileOutputStream out = new FileOutputStream(tempFile)) {
      byte[] buffer = new byte[4096];
      int bytesRead;
      while ((bytesRead = in.read(buffer)) != -1) {
        out.write(buffer, 0, bytesRead);
      }
    }
    return tempFile;
  }

  /**
   * 将远程 URL 的 PDF 文件下载为本地临时文件 pdf
   */
  public static File downloadPdfToLocal(String fileUrl) throws IOException {
    URL url = new URL(fileUrl);
    File tempFile = File.createTempFile("esign-pdf-", ".pdf"); // 创建临时 PDF 文件
    tempFile.deleteOnExit(); // JVM 关闭时兜底删除

    try (InputStream in = url.openStream();
         FileOutputStream out = new FileOutputStream(tempFile)) {
      byte[] buffer = new byte[4096];
      int bytesRead;
      while ((bytesRead = in.read(buffer)) != -1) {
        out.write(buffer, 0, bytesRead);
      }
    }

    return tempFile;
  }

  /**
   * 安全删除临时文件
   */
  public static void safeDeleteFile(File file) {
    if (file != null && file.exists()) {
      boolean deleted = file.delete();
      if (!deleted) {
        LOGGER.error("⚠️ 删除临时文件失败: " + file.getAbsolutePath());
      }
    }
  }

}
