package com.sing.service.util;

import java.util.UUID;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.singservice.sing_service.util.UUIDUtils
 * @作者 Mr.sandman
 * @时间 2025/05/22 14:10
 */
public class UUIDUtils {

  private static final String OSSKey  = "ossKey";

    /**
     * 获取带oss相关的UUID
     * @return UUID
     */
    public static String generateCustomUUID() {
      // 拼接前缀 "OSS" 并返回结果
      return OSSKey + UUID.randomUUID().toString().replaceAll("-", "");
    }

    public static String generateContractUUID() {
      return UUID.randomUUID().toString().replaceAll("-", "");
    }

}
