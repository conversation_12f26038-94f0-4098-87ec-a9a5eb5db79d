package com.sing.service.util.enums;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.sing.service.util.enums.TrafficSignalEnum
 * @作者 Mr.sandman
 * @时间 2025/06/20 11:09
 */

@Getter
@AllArgsConstructor
public enum TrafficSignalEnum {
  GREEN_SIGNAL("LVXIN", "绿信"),
  PPCJDL("PPCJDL", "拍拍");

  private final String code;
  private final String desc;
}
