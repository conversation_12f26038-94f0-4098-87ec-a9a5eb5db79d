package com.maguo.loan.cash.flow.entity.vo;

import java.math.BigDecimal;

/**
 * 还款计划文件
 * @公司 中数金智(上海)有限公司
 * @包名 com.maguo.loan.cash.flow.entity.vo.RepaymentVo
 * @作者 Mr.sandman
 * @时间 2025/05/28 16:03
 */
public class RepayPlanVo {

    /**
     * 对方业务号
     */
    private String outApplSeq;

    /**
     * 借据号
     */
    private String loanNo;

    /**
     * 期数
     */
    private String thr;

    /**
     * 到期日（格式：yyyy-MM-dd）
     */
    private String dueDt;

    /**
     * 期供金额（本期应还总额）
     */
    private BigDecimal instmAmt;

    /**
     * 本金金额
     */
    private BigDecimal prcpAmt;

    /**
     * 利息金额
     */
    private BigDecimal intAmt;

    /**
     * 罚息金额
     */
    private BigDecimal odIntAmt;

    /**
     * 复利金额
     */
    private BigDecimal commIntAmt;

    /**
     * 担保费金额
     */
    private BigDecimal guaranteeFeeAmt;

    /**
     * 担保费逾期费用
     */
    private BigDecimal guaranteeFeeOdAmt;

    /**
     * 咨询费
     */
    private BigDecimal consultFee;

    // Getter and Setter 方法
    public String getOutApplSeq() {
        return outApplSeq;
    }

    public void setOutApplSeq(String outApplSeq) {
        this.outApplSeq = outApplSeq;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getThr() {
        return thr;
    }

    public void setThr(String thr) {
        this.thr = thr;
    }

    public String getDueDt() {
        return dueDt;
    }

    public void setDueDt(String dueDt) {
        this.dueDt = dueDt;
    }

    public BigDecimal getInstmAmt() {
        return instmAmt;
    }

    public void setInstmAmt(BigDecimal instmAmt) {
        this.instmAmt = instmAmt;
    }

    public BigDecimal getPrcpAmt() {
        return prcpAmt;
    }

    public void setPrcpAmt(BigDecimal prcpAmt) {
        this.prcpAmt = prcpAmt;
    }

    public BigDecimal getIntAmt() {
        return intAmt;
    }

    public void setIntAmt(BigDecimal intAmt) {
        this.intAmt = intAmt;
    }

    public BigDecimal getOdIntAmt() {
        return odIntAmt;
    }

    public void setOdIntAmt(BigDecimal odIntAmt) {
        this.odIntAmt = odIntAmt;
    }

    public BigDecimal getCommIntAmt() {
        return commIntAmt;
    }

    public void setCommIntAmt(BigDecimal commIntAmt) {
        this.commIntAmt = commIntAmt;
    }

    public BigDecimal getGuaranteeFeeAmt() {
        return guaranteeFeeAmt;
    }

    public void setGuaranteeFeeAmt(BigDecimal guaranteeFeeAmt) {
        this.guaranteeFeeAmt = guaranteeFeeAmt;
    }

    public BigDecimal getGuaranteeFeeOdAmt() {
        return guaranteeFeeOdAmt;
    }

    public void setGuaranteeFeeOdAmt( BigDecimal guaranteeFeeOdAmt ) {
        this.guaranteeFeeOdAmt = guaranteeFeeOdAmt;
    }

    public BigDecimal getConsultFee() {
        return consultFee;
    }

    public void setConsultFee( BigDecimal consultFee ) {
        this.consultFee = consultFee;
    }

    @Override
    public String toString() {
        return "LoanInstallment{" +
            "outApplSeq='" + outApplSeq + '\'' +
            ", loanNo='" + loanNo + '\'' +
            ", thr='" + thr + '\'' +
            ", dueDt='" + dueDt + '\'' +
            ", instmAmt=" + instmAmt +
            ", prcpAmt=" + prcpAmt +
            ", intAmt=" + intAmt +
            ", odIntAmt=" + odIntAmt +
            ", commIntAmt=" + commIntAmt +
            ", guaranteeFeeAmt=" + guaranteeFeeAmt +
            ", guaranteeFeeOdAmt=" + guaranteeFeeOdAmt +
            '}';
    }

}
