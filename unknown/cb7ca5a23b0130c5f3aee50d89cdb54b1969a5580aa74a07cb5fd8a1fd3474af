package com.maguo.loan.cash.flow.entity.vo;

import java.math.BigDecimal;

/**
 * 还款明细文件
 * @公司 中数金智(上海)有限公司
 * @包名 com.maguo.loan.cash.flow.entity.vo.RepayDetailVo
 * @作者 Mr.sandman
 * @时间 2025/05/28 16:05
 */
public class RepayDetailVo {

    /**
     * 借据号
     */
    private String loanNo;
    /**
     * 还款申请流水号
     */
    private String repaymentSeq;
    /**
     * 记账流水号
     */
    private String setlSeq;
    /**
     * 对方业务号
     */
    private String outApplSeq;
    /**
     * 客户编号
     */
    private String custId;
    /**
     * 还款模式
     * <p>
     * 取值说明：
     * 01：到期日及之后的还款，一次归还一期
     * 02：提前结清
     * 04：归还欠款
     * 06：提前还一期
     */
    private String payMode;
    /**
     * 实际还款日期，格式为 yyyy-MM-dd
     */
    private String setlDt;
    /**
     * 实际还款时间，格式为 yyyy-MM-dd hh:mm:ss
     */
    private String setlTime;
    /**
     * 是否扣款
     * <p>
     * 取值说明：
     * Y：是
     * N：否
     */
    private String isCy;
    /**
     * 还款金额
     */
    private BigDecimal totalAmt;
    /**
     * 归还本金
     */
    private BigDecimal prcpAmt;
    /**
     * 归还利息
     */
    private BigDecimal intAmt;
    /**
     * 归还罚息
     */
    private BigDecimal odIntAmt;
    /**
     * 归还复利
     */
    private BigDecimal commOdIntAmt;
    /**
     * 咨询费
     */
    private BigDecimal consultFee;
    /**
     * 违约金
     */
    private BigDecimal breachAmt;
    /**
     * 还款后贷款余额
     */
    private BigDecimal currPrincipal;
    /**
     * 是否代偿
     * <p>
     * 取值说明：
     * Y：是
     * N：否
     */
    private String isDc;
    /**
     * 归还担保费
     */
    private BigDecimal guaranteeFeeAmt;
    /**
     * 归还担保费逾期费用
     */
    private BigDecimal guaranteeFeeOdAmt;
    /**
     * 商户订单号，发送给支付渠道的商户订单号
     */
    private String platformFlowNo;

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo( String loanNo ) {
        this.loanNo = loanNo;
    }

    public BigDecimal getCurrPrincipal() {
        return currPrincipal;
    }

    public void setCurrPrincipal( BigDecimal currPrincipal ) {
        this.currPrincipal = currPrincipal;
    }

    public String getRepaymentSeq() {
        return repaymentSeq;
    }

    public void setRepaymentSeq( String repaymentSeq ) {
        this.repaymentSeq = repaymentSeq;
    }

    public String getSetlSeq() {
        return setlSeq;
    }

    public void setSetlSeq( String setlSeq ) {
        this.setlSeq = setlSeq;
    }

    public String getOutApplSeq() {
        return outApplSeq;
    }

    public void setOutApplSeq( String outApplSeq ) {
        this.outApplSeq = outApplSeq;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId( String custId ) {
        this.custId = custId;
    }

    public String getPayMode() {
        return payMode;
    }

    public void setPayMode( String payMode ) {
        this.payMode = payMode;
    }

    public String getSetlDt() {
        return setlDt;
    }

    public void setSetlDt( String setlDt ) {
        this.setlDt = setlDt;
    }

    public String getSetlTime() {
        return setlTime;
    }

    public void setSetlTime( String setlTime ) {
        this.setlTime = setlTime;
    }

    public String getIsCy() {
        return isCy;
    }

    public void setIsCy( String isCy ) {
        this.isCy = isCy;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt( BigDecimal totalAmt ) {
        this.totalAmt = totalAmt;
    }

    public BigDecimal getPrcpAmt() {
        return prcpAmt;
    }

    public void setPrcpAmt( BigDecimal prcpAmt ) {
        this.prcpAmt = prcpAmt;
    }

    public BigDecimal getIntAmt() {
        return intAmt;
    }

    public void setIntAmt( BigDecimal intAmt ) {
        this.intAmt = intAmt;
    }

    public BigDecimal getOdIntAmt() {
        return odIntAmt;
    }

    public void setOdIntAmt( BigDecimal odIntAmt ) {
        this.odIntAmt = odIntAmt;
    }

    public BigDecimal getCommOdIntAmt() {
        return commOdIntAmt;
    }

    public void setCommOdIntAmt( BigDecimal commOdIntAmt ) {
        this.commOdIntAmt = commOdIntAmt;
    }

    public BigDecimal getConsultFee() {
        return consultFee;
    }

    public void setConsultFee( BigDecimal consultFee ) {
        this.consultFee = consultFee;
    }

    public BigDecimal getBreachAmt() {
        return breachAmt;
    }

    public void setBreachAmt( BigDecimal breachAmt ) {
        this.breachAmt = breachAmt;
    }

    public String getIsDc() {
        return isDc;
    }

    public void setIsDc( String isDc ) {
        this.isDc = isDc;
    }

    public BigDecimal getGuaranteeFeeAmt() {
        return guaranteeFeeAmt;
    }

    public void setGuaranteeFeeAmt( BigDecimal guaranteeFeeAmt ) {
        this.guaranteeFeeAmt = guaranteeFeeAmt;
    }

    public BigDecimal getGuaranteeFeeOdAmt() {
        return guaranteeFeeOdAmt;
    }

    public void setGuaranteeFeeOdAmt( BigDecimal guaranteeFeeOdAmt ) {
        this.guaranteeFeeOdAmt = guaranteeFeeOdAmt;
    }

    public String getPlatformFlowNo() {
        return platformFlowNo;
    }

    public void setPlatformFlowNo( String platformFlowNo ) {
        this.platformFlowNo = platformFlowNo;
    }
}
