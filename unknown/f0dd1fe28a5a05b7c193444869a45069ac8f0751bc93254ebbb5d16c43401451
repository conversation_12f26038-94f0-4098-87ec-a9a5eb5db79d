package com.maguo.loan.cash.flow.enums;

public enum OccupationCode {
    ONE(1, "其他"),
    TEN(10, "普通员工"),
    TWENTY(20, "基层管理人员"),
    THIRTY(30, "中层管理人员"),
    FOURTY(40, "高层管理人员"),
    FIFTY(50, "经营者");
    private Integer code;
    private String desc;
    OccupationCode(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public Integer getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }
}
